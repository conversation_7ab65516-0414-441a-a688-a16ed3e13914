{"cells": [{"cell_type": "markdown", "id": "3ac15c7d", "metadata": {}, "source": ["# PDF EXTRACTION"]}, {"cell_type": "code", "execution_count": 112, "id": "6db479a9", "metadata": {}, "outputs": [], "source": ["# Your Google Gemini API key\n", "GEMINI_API_KEY = \"AIzaSyAqeGEgGgAwSuF-1UcimRSa64-973-5cB4\""]}, {"cell_type": "markdown", "id": "6b48c711", "metadata": {}, "source": ["# TRY"]}, {"cell_type": "code", "execution_count": 113, "id": "d0c41395", "metadata": {}, "outputs": [], "source": ["import google.generativeai as genai\n", "import PyPDF2\n", "from PIL import Image\n", "import pdf2image\n", "import io\n", "import os\n", "import re\n", "import json\n", "import pandas as pd\n", "\n", "def extract_text_from_pdf_gemini(pdf_path, api_key):\n", "    \"\"\"\n", "    Extract text from a PDF file using Google Gemini Flash 1.5\n", "   \n", "    Args:\n", "        pdf_path (str): Path to the PDF file\n", "        api_key (str): Your Google Gemini API key\n", "       \n", "    Returns:\n", "        str: All extracted text from the PDF\n", "    \"\"\"\n", "    try:\n", "        # Configure Gemini\n", "        genai.configure(api_key=api_key)\n", "        model = genai.GenerativeModel('gemini-2.5-flash')\n", "       \n", "        # First try PyPDF2 for text-based PDFs\n", "        try:\n", "            with open(pdf_path, 'rb') as file:\n", "                pdf_reader = PyPDF2.PdfReader(file)\n", "                text = \"\"\n", "                for page in pdf_reader.pages:\n", "                    text += page.extract_text() + \"\\n\"\n", "               \n", "                # If we got meaningful text, return it\n", "                if text.strip() and len(text.strip()) > 50:\n", "                    return text.strip()\n", "        except:\n", "            pass\n", "       \n", "        # If PyPDF2 failed or returned minimal text, use Gemini with images\n", "        print(\"Converting PDF to images for OCR...\")\n", "       \n", "        # Convert PDF pages to images\n", "        images = pdf2image.convert_from_path(pdf_path)\n", "       \n", "        all_text = []\n", "       \n", "        for i, image in enumerate(images):\n", "            print(f\"Processing page {i+1}/{len(images)}\")\n", "           \n", "            # Convert PIL image to bytes\n", "            img_byte_arr = io.BytesIO()\n", "            image.save(img_byte_arr, format='PNG')\n", "            img_byte_arr = img_byte_arr.getvalue()\n", "           \n", "            # Upload image to Gemini\n", "            uploaded_file = genai.upload_file(\n", "                path=None,\n", "                mime_type=\"image/png\",\n", "                data=img_byte_arr\n", "            )\n", "           \n", "            # Extract text using Gemini\n", "            response = model.generate_content([\n", "                uploaded_file,\n", "                \"Extract all the text from this image. Return the text content and the tickboxes, no explanations or formatting.\"\n", "            ])\n", "           \n", "            if response.text:\n", "                all_text.append(response.text.strip())\n", "           \n", "            # Clean up uploaded file\n", "            genai.delete_file(uploaded_file.name)\n", "       \n", "        return '\\n\\n'.join(all_text)\n", "       \n", "    except Exception as e:\n", "        print(f\"Error: {e}\")\n", "        return None\n", " \n", "def extract_text_simple_gemini(pdf_path, api_key):\n", "    \"\"\"\n", "    Simple text extraction using Gemini Flash 1.5 by uploading PDF directly\n", "   \n", "    Args:\n", "        pdf_path (str): Path to the PDF file\n", "        api_key (str): Your Google Gemini API key\n", "       \n", "    Returns:\n", "        str: All extracted text from the PDF\n", "    \"\"\"\n", "    try:\n", "        # Configure Gemini\n", "        genai.configure(api_key=api_key)\n", "        model = genai.GenerativeModel('gemini-2.5-flash')\n", "       \n", "        print(f\"Uploading PDF: {pdf_path}\")\n", "       \n", "        # Upload PDF directly to Gemini\n", "        uploaded_file = genai.upload_file(\n", "            path=pdf_path,\n", "            mime_type=\"application/pdf\"\n", "        )\n", "       \n", "        print(\"Extracting text...\")\n", "       \n", "        # Extract text using Gemini\n", "        response = model.generate_content([\n", "            uploaded_file,\n", "            \"Extract all the text from this PDF document. create a json of that for validation\"\n", "        ])\n", "       \n", "        # Clean up uploaded file\n", "        genai.delete_file(uploaded_file.name)\n", "       \n", "        if response.text:\n", "            return response.text.strip()\n", "        else:\n", "            return \"No text extracted\"\n", "           \n", "    except Exception as e:\n", "        print(f\"Error: {e}\")\n", "        return None\n", "\n", "def extract_text_excel_gemini(excel_path, api_key):\n", "    \"\"\"\n", "    Simple text extraction using Gemini Flash 1.5 by uploading Excel file directly\n", "    \n", "    Args:\n", "        excel_path (str): Path to the Excel file (.xlsx, .xls)\n", "        api_key (str): Your Google Gemini API key\n", "        \n", "    Returns:\n", "        str: All extracted text from the Excel file\n", "    \"\"\"\n", "    try:\n", "        # Configure Gemini\n", "        genai.configure(api_key=api_key)\n", "        model = genai.GenerativeModel('gemini-2.5-flash')\n", "        \n", "        print(f\"Uploading Excel file: {excel_path}\")\n", "        \n", "        # Determine MIME type based on file extension\n", "        if excel_path.lower().endswith('.xlsx'):\n", "            mime_type = \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"\n", "        elif excel_path.lower().endswith('.xls'):\n", "            mime_type = \"application/vnd.ms-excel\"\n", "        else:\n", "            raise ValueError(\"Unsupported file format. Please use .xlsx or .xls files.\")\n", "        \n", "        # Upload Excel file directly to Gemini\n", "        uploaded_file = genai.upload_file(\n", "            path=excel_path,\n", "            mime_type=mime_type\n", "        )\n", "        \n", "        print(\"Extracting text...\")\n", "        \n", "        # Extract text using Gemini\n", "        response = model.generate_content([\n", "            uploaded_file,\n", "            \"Extract all the text content from this Excel file. Include data from all worksheets, column headers, and cell values. Create a JSON structure for validation that organizes the data by worksheet.\"\n", "        ])\n", "        \n", "        # Clean up uploaded file\n", "        genai.delete_file(uploaded_file.name)\n", "        \n", "        if response.text:\n", "            return response.text.strip()\n", "        else:\n", "            return \"No text extracted\"\n", "            \n", "    except Exception as e:\n", "        print(f\"Error: {e}\")\n", "        return None\n", "\n", "def extract_and_parse_json(text):\n", "    try:\n", "        # Extract substring starting from first {\n", "        match = re.search(r'\\{.*\\}', text, re.DOTALL)\n", "        if match:\n", "            json_str = match.group()\n", "            # print(json_str)\n", "            return json.loads(json_str)\n", "        else:\n", "            print(\"No JSON object found.\")\n", "            return None\n", "    except json.JSONDecodeError as e:\n", "        print(\"Invalid JSON format:\", e)\n", "        return None \n"]}, {"cell_type": "code", "execution_count": 114, "id": "d4d6b059", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Uploading PDF: 01. PSW - Part Submission Warrant.pdf\n", "Extracting text...\n", "{'DocumentInformation': {'Title': 'Part Submission Warrant', 'FormatNo': 'SWARAJ / S-PPAP / 01', 'Brand': 'Swaraj', 'FormIdentifier': 'CFG-1001', 'FormDate': 'March 2006'}, 'PartInformation': {'PartName': 'LOWER LINK ASSY.', 'ShownOnDrawingNumber': 'P80009246A', 'EngineeringChangeLevel': 'A', 'AdditionalEngineeringChanges': None, 'SafetyAndGovernmentRegulation': 'Yes', 'CheckingAidNo': None, 'CheckingAidEngineeringChangeLevel': None}, 'CustomerSubmittalInformation': {'CustomerPartNumber': 'P80009246A', 'OriginalPartNumber': 'P80009246A', 'OriginalPartNumberDated': '29-May-24', 'PurchaseOrderNo': '6711009828', 'WeightKg': '5.02', 'WeightDated': None}, 'OrganizationManufacturingInformation': {'OrganizationNameAndSupplierVendorCode': 'JAYCEE STRIPS AND FASTENERS PVT. LTD. VENDOR CODE DDJ00202AA', 'StreetAddress': '386, FOCAL POINT,', 'City': 'AMRITSAR', 'Region': 'PUNJAB', 'PostalCode': '143001', 'Country': 'INDIA'}, 'CustomerDetails': {'CustomerNameDivision': 'MAHINDRA & MAHINDRA LTD.', 'BuyerBuyerCode': 'MR. VARUN KUMAR', 'Application': None}, 'MaterialsReporting': {'SubstancesOfConcernReported': 'Yes', 'SubmittedByIMDSOrOtherCustomerFormat': None, 'PolymericPartsIdentifiedWithISOMarkingCodes': 'n/a'}, 'ReasonForSubmission': {'InitialSubmission': True, 'EngineeringChanges': False, 'ToolingTransferReplacementRefurbishmentAdditional': False, 'CorrectionOfDiscrepancy': False, 'ToolingInactiveGreaterThan1Year': False, 'ChangeToOptionalConstructionOrMaterial': False, 'SupplierOrMaterialSourceChange': False, 'ChangeInPartProcessing': False, 'PartsProducedAtAdditionalLocation': False, 'OtherPleaseSpecifyBelow': None}, 'RequestedSubmissionLevel': {'Level1': False, 'Level2': False, 'Level3': True, 'Level4': False, 'LevelS': False}, 'SubmissionResults': {'DimensionalMeasurements': True, 'MaterialAndFunctionalTests': True, 'AppearanceCriteria': True, 'StatisticalProcessPackage': True, 'ResultsMeetDrawingAndSpecificationRequirements': 'Yes', 'MoldCavityProductionProcess': None}, 'Declaration': {'ProductionRate': '60 No.s 8 hours.', 'ExplanationComments': None, 'CustomerToolProperlyTaggedAndNumbered': 'Yes'}, 'OrganizationAuthorizedSignatureDetails': {'PrintName': 'KANAV AGGARWAL', 'PhoneNumber': '9988145678', 'Title': None, 'Email': '<EMAIL>', 'Date': None, 'FaxNumber': None}, 'ForCustomerUseOnly': {'PartWarrantDisposition': {'Approved': False, 'Rejected': False, 'Other': None}, 'CustomerSignature': None, 'Date': None, 'PrintName': None, 'CustomerTrackingNumberOptional': None}}\n"]}], "source": ["# Your Google Gemini API key\n", "GEMINI_API_KEY = \"AIzaSyAqeGEgGgAwSuF-1UcimRSa64-973-5cB4\"\n", "\n", "# PDF file path\n", "pdf_file = \"01. PSW - Part Submission Warrant.pdf\"\n", "\n", "text1 = extract_text_simple_gemini(pdf_file, GEMINI_API_KEY)\n", "\n", "PSW = extract_and_parse_json(text1)\n", "print(PSW)"]}, {"cell_type": "code", "execution_count": 115, "id": "ab9935ca", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Uploading PDF: C:\\Users\\<USER>\\Desktop\\swaraj\\files\\PFD - P80009246A.pdf\n", "Extracting text...\n"]}], "source": ["pfd = extract_text_simple_gemini(r\"C:\\Users\\<USER>\\Desktop\\swaraj\\files\\PFD - P80009246A.pdf\", GEMINI_API_KEY)\n", "\n", "pfd_result = extract_and_parse_json(pfd)"]}, {"cell_type": "markdown", "id": "4feb5f39", "metadata": {}, "source": ["# IMAGES"]}, {"cell_type": "code", "execution_count": 116, "id": "8fffaf97", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processing: C:\\Users\\<USER>\\Desktop\\swaraj\\uploads\\images\\Screenshot 2025-07-11 130345.png\n", "Processing: C:\\Users\\<USER>\\Desktop\\swaraj\\uploads\\images\\Screenshot 2025-07-11 130357.png\n", "Processing: C:\\Users\\<USER>\\Desktop\\swaraj\\uploads\\images\\Screenshot 2025-07-11 130404.png\n", "Processing: C:\\Users\\<USER>\\Desktop\\swaraj\\uploads\\images\\Screenshot 2025-07-11 130413.png\n", "Processing: C:\\Users\\<USER>\\Desktop\\swaraj\\uploads\\images\\Screenshot 2025-07-11 130440.png\n", "Processing: C:\\Users\\<USER>\\Desktop\\swaraj\\uploads\\images\\Screenshot 2025-07-11 130458.png\n", "Processing: C:\\Users\\<USER>\\Desktop\\swaraj\\uploads\\images\\Screenshot 2025-07-11 130505.png\n"]}], "source": ["import os\n", "import google.generativeai as genai\n", "\n", "def extract_text_from_images_in_folder(folder_path, api_key, output_json_path=None):\n", "    \"\"\"\n", "    Extracts text from all images in a folder using Gemini 2.5 Flash.\n", "\n", "    Args:\n", "        folder_path (str): Path to the folder containing images.\n", "        api_key (str): Your Gemini API key.\n", "        output_json_path (str, optional): File path to save results as JSON.\n", "\n", "    Returns:\n", "        dict: Mapping of filenames to extracted text.\n", "    \"\"\"\n", "    import json\n", "\n", "    genai.configure(api_key=api_key)\n", "    model = genai.GenerativeModel('gemini-2.5-flash')\n", "\n", "    results = {}\n", "    supported_extensions = ('.png', '.jpg', '.jpeg', '.webp')\n", "\n", "    for filename in os.listdir(folder_path):\n", "        if filename.lower().endswith(supported_extensions):\n", "            file_path = os.path.join(folder_path, filename)\n", "            print(f\"Processing: {file_path}\")\n", "\n", "            try:\n", "                mime_type = \"image/png\" if filename.lower().endswith(\".png\") else \"image/jpeg\"\n", "                uploaded_file = genai.upload_file(path=file_path, mime_type=mime_type)\n", "\n", "                response = model.generate_content([\n", "                    uploaded_file,\n", "                    \"\"\"Extract all the following data from images if avialable and Return in JSON\n", "                        IF IMAGE CONTAIN INFORMATION LIKE THESE:- DRG. No., Qty, Description, Size/Specification, Matl., RMKS\n", "                        iT'S TYPE 'A'\n", "                        RESPONSE IN THIS FROMAT \n", "                        EXAMPLE:- {\n", "                                \"table_type\": \"TYPE A\",\n", "                                \"data\": [\n", "                                    {\n", "                                    \"S. No.\": ,\n", "                                    \"DRG. No.\": ,\n", "                                    \"Qty\": ,\n", "                                    \"Description\": ,\n", "                                    \"Size/Specification\": ,\n", "                                    \"Matl.\": ,\n", "                                    \"RMKS\": \n", "                                    },\n", "                                    {\n", "                                    \"S. No.\": ,\n", "                                    \"DRG. No.\": ,\n", "                                    \"Qty\": ,\n", "                                    \"Description\": ,\n", "                                    \"Size/Specification\": ,\n", "                                    \"Matl.\": ,\n", "                                    \"RMKS\": \n", "                                    }\n", "                                ]\n", "                                }\n", "\n", "                        \n", "                        IF IMAGE CONTAIN INFORMATION LIKE THESE:- DATE ,PART ,DRAWING NO. ,REV ,WEIGHT  ,MATERIAL\n", "                        iT'S TYPE 'B' \n", "                        RESPONSE IN THIS FROMAT \n", "                        EXAMPLE:- {\n", "                                \"table_type\": \"TYPE B\",\n", "                                \"data\": \n", "                                    {\n", "                                    \"DATE\": ,\n", "                                    \"PART\": ,\n", "                                    \"DRAWING NO.\": ,\n", "                                    \"REV\": ,\n", "                                    \"WEIGHT\": ,\n", "                                    \"MATERIAL\": \n", "                                    \"Engineering Change level\": \"001\"\n", "                                    }            \n", "                        }\n", "                    \"\"\"\n", "                ])\n", "\n", "                genai.delete_file(uploaded_file.name)\n", "\n", "                if response.text:\n", "                    results[filename] = response.text.strip()\n", "                else:\n", "                    results[filename] = \"No text extracted\"\n", "\n", "            except Exception as e:\n", "                print(f\"Error processing {filename}: {e}\")\n", "                results[filename] = f\"Error: {e}\"\n", "\n", "    if output_json_path:\n", "        with open(output_json_path, 'w', encoding='utf-8') as f:\n", "            json.dump(results, f, indent=4, ensure_ascii=False)\n", "\n", "    return results\n", "\n", "api_key = GEMINI_API_KEY\n", "folder = r\"C:\\Users\\<USER>\\Desktop\\swaraj\\uploads\\images\"\n", "output_json = \"extracted_texts.json\"\n", "\n", "results = extract_text_from_images_in_folder(folder, api_key, output_json_path=output_json)\n", "def extract_full_json_block(text):\n", "    start = text.find('{')\n", "    end = text.rfind('}')\n", "    if start != -1 and end != -1 and end > start:\n", "        return text[start:end+1]\n", "    return None\n", "\n", "images_json=[]\n", "for file, text in results.items():\n", "    result = extract_full_json_block(text)\n", "    json_obj = json.loads(result)\n", "    images_json.append(json_obj)\n"]}, {"cell_type": "markdown", "id": "07213ab9", "metadata": {}, "source": ["# Part Submission Warrant"]}, {"cell_type": "code", "execution_count": 117, "id": "78733164", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Uploading PDF: 01. PSW - Part Submission Warrant.pdf\n", "Extracting text...\n"]}, {"data": {"text/plain": ["{'part_name': 'LOWER LINK ASSY.',\n", " 'customer_part_number': 'P80009246A',\n", " 'org_part_number': 'P80009246A',\n", " 'engineering_change_level': 'A',\n", " 'date': '29-May-24',\n", " 'purchase_order_no': '6711009828',\n", " 'weight': '5.02',\n", " 'checking_aid_no': '',\n", " 'checking_aid_engineering_change_level': '',\n", " 'organization_manufacturing_info': {'vendor_name': 'JAYCEE STRIPS AND FASTENERS PVT. LTD.',\n", "  'vendor_code': 'DDJ00202AA',\n", "  'vendor_address': '386, FOCAL POINT, AMRITSAR, PUNJAB, 143001, INDIA'},\n", " 'customer_submission_info': 'MAHINDRA & MAHINDRA LTD.',\n", " 'po_buyer_name': 'MR. VARUN KUMAR',\n", " 'reason_for_submission': 'Initial Submission',\n", " 'level_of_submission': 'Level 3 - Warrant with product samples and complete supporting data submitted to customer.',\n", " 'psw_signed_by_supplier': True,\n", " 'production_rate': '60 No.s 8 hours.'}"]}, "execution_count": 117, "metadata": {}, "output_type": "execute_result"}], "source": ["def extract_text_simple_gemini_psw(pdf_path, api_key):\n", "    \"\"\"\n", "    Simple text extraction using Gemini Flash 1.5 by uploading PDF directly\n", "   \n", "    Args:\n", "        pdf_path (str): Path to the PDF file\n", "        api_key (str): Your Google Gemini API key\n", "       \n", "    Returns:\n", "        str: All extracted text from the PDF\n", "    \"\"\"\n", "    try:\n", "        # Configure Gemini\n", "        genai.configure(api_key=api_key)\n", "        model = genai.GenerativeModel('gemini-2.5-flash')\n", "       \n", "        print(f\"Uploading PDF: {pdf_path}\")\n", "       \n", "        # Upload PDF directly to Gemini\n", "        uploaded_file = genai.upload_file(\n", "            path=pdf_path,\n", "            mime_type=\"application/pdf\"\n", "        )\n", "       \n", "        print(\"Extracting text...\")\n", "       \n", "        # Extract text using Gemini\n", "        response = model.generate_content([\n", "            uploaded_file,\n", "                    \"\"\"Extract folowwing information from this image. Return Dictionary format along with these details.\n", "                        /{\n", "                        \"part_name\": \"\",\n", "                        \"customer_part_number\": \"\",\n", "                        \"org_part_number\":\"\",\n", "                        \"engineering_change_level\": \"\",\n", "                        \"date\": \"\",\n", "                        \"purchase_order_no\": \"\",  # Shouldn't be blank\n", "                        \"weight\": \"\",\n", "                        \"checking_aid_no\": \"\",\n", "                        \"checking_aid_engineering_change_level\": \"\",\n", "                        \"organization_manufacturing_info\": {\n", "                            \"vendor_name\": \"\",\n", "                            \"vendor_code\": \"\",\n", "                            \"vendor_address\": \"\"\n", "                        },\n", "                        \"customer_submission_info\": \"\",\n", "                        \"po_buyer_name\": \"\",\n", "                        \"reason_for_submission\": \"\",  # Shouldn't be blank\n", "                        \"level_of_submission\": \"\",\n", "                        \"psw_signed_by_supplier\": , # False or True\n", "                        \"production_rate\": \"\" \n", "\n", "                        /}\n", "                    \"\"\"\n", "        ])\n", "       \n", "        # Clean up uploaded file\n", "        genai.delete_file(uploaded_file.name)\n", "       \n", "        if response.text:\n", "            return response.text.strip()\n", "        else:\n", "            return \"No text extracted\"\n", "           \n", "    except Exception as e:\n", "        print(f\"Error: {e}\")\n", "        return None\n", "    \n", "pdf_file = \"01. PSW - Part Submission Warrant.pdf\"\n", "\n", "text1 = extract_text_simple_gemini_psw(pdf_file, GEMINI_API_KEY)\n", "\n", "PSW = extract_and_parse_json(text1)\n", "PSW"]}, {"cell_type": "markdown", "id": "39fd015a", "metadata": {}, "source": ["# Process Flow Diagram"]}, {"cell_type": "code", "execution_count": 118, "id": "f4b06946", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Uploading PDF: C:\\Users\\<USER>\\Desktop\\swaraj\\files\\PFD - P80009246A.pdf\n", "Extracting text...\n"]}, {"data": {"text/plain": ["{'Supplier Information': {'Supplier Name': 'JAYCEE STRIPS AND FASTENERS PVT. LTD.',\n", "  'Supplier Code': 'DDJ00202AA',\n", "  'Part Name': 'LOWER LINK ASSY.',\n", "  'Part Number': 'P80009246A',\n", "  'Drg. Latest Alteration': 'A,001;29.05.2024'},\n", " 'Process Flow Details': {'FLAT': {'Processes': {'CUTTING': '10',\n", "    '1st SIDE CHAMFER GRINDING': '20',\n", "    '2nd SIDE CHAMFER GRINDING': '30',\n", "    'VMC DRILLING': '40',\n", "    'CHAMFERING': '50',\n", "    'PRE-HEATING & BENDING 1': '60',\n", "    'PRE-HEATING & BENDING-2': '70',\n", "    'HARDENING & TEMPERING': '80'}},\n", "  'BALL END-1': {'Processes': {'BOUGHT OUT PART (FORGING)': '90a',\n", "    'ROUGH DRILLING': '90',\n", "    'COPY TURNING': '100',\n", "    'TAPER TURNING': '110'}},\n", "  'BALL END-2': {'Processes': {'BOUGHT OUT PART (FORGING)': '120a',\n", "    'ROUGH DRILLING': '120',\n", "    'COPY TURNING': '130',\n", "    'TAPER TURNING': '140'}},\n", "  'BALL CAT 1': {'Processes': {'B.O.P - MACHINING': '150a',\n", "    'HARDENING & TEMPERING': '150',\n", "    'SHOT BLASTING': '160',\n", "    'PLATING': '170'}},\n", "  'LOWER LINK (SUB ASSY.)': {'Processes': {'PART a, b & c (TACKING & WELDING)': '180',\n", "    'WELD JOINT NORMALIZING': '190',\n", "    'SHOT BLASTING': '200',\n", "    'PRIMER COATING (CHARCOAL GREY COLOUR)': '210'}},\n", "  'LOWER LINK ASSY.': {'Processes': {'PART d, e (BALL CRIMPING)': '220',\n", "    'TRACEBILITY CODE (PUNCHING)': '230'}}}}"]}, "execution_count": 118, "metadata": {}, "output_type": "execute_result"}], "source": ["#--------------------------------------------------LLM CALL And Prompt for Process Flow Diagram-----------------------------------------\n", "\n", "def extract_text_simple_gemini_pfd(pdf_path, api_key):\n", "    \"\"\"\n", "    Simple text extraction using Gemini Flash 1.5 by uploading PDF directly\n", "   \n", "    Args:\n", "        pdf_path (str): Path to the PDF file\n", "        api_key (str): Your Google Gemini API key\n", "       \n", "    Returns:\n", "        str: All extracted text from the PDF\n", "    \"\"\"\n", "    try:\n", "        # Configure Gemini\n", "        genai.configure(api_key=api_key)\n", "        model = genai.GenerativeModel('gemini-2.5-flash')\n", "       \n", "        print(f\"Uploading PDF: {pdf_path}\")\n", "       \n", "        # Upload PDF directly to Gemini\n", "        uploaded_file = genai.upload_file(\n", "            path=pdf_path,\n", "            mime_type=\"application/pdf\"\n", "        )\n", "       \n", "        print(\"Extracting text...\")\n", "       \n", "        # Extract text using Gemini\n", "        response = model.generate_content([\n", "            uploaded_file,\n", "                    \"\"\"Extract folowwing information from this image. Return Dictionary format along with these details.\n", "                        Details (Supplier name, Supplier Code, Part Number, Part Name and Latest Alteration)\n", "                        Check all Child parts from BOM table AND NHUMBER OF EVERY PROCESS EXAMOPLE: \n", "                        CUTTING : 10\n", "\n", "                        we want output like this:-\n", "                        Example:-\n", "                          {\n", "                                \"Supplier Information\": {\n", "                                    \"Supplier Name\": \"JAYCEE STRIPS AND FASTENERS PVT. LTD.\",\n", "                                    \"Supplier Code\": \"DDJ00202AA\",\n", "                                    \"Part Name\": \"LOWER LINK ASSY.\",\n", "                                    \"Part Number\": \"P80009246A\",\n", "                                    \"Drg. Latest Alteration\": \"A,001;29.05.2024\"\n", "                                },\n", "                                \"Process Flow Details\": {\n", "                                    \"FLAT\": {\n", "                                        \"Processes\": {\n", "                                            \"CUTTING\": \"10\",\n", "                                            \"1st SIDE CHAMFER GRINDING\": \"20\",\n", "                                            \"2nd SIDE CHAMFER GRINDING\": \"30\",\n", "                                            \"VMC DRILLING\": \"40\",\n", "                                            \"CHAMFERING\": \"50\",\n", "                                            \"PRE-HEATING & BENDING 1\": \"60\",\n", "                                            \"PRE-HEATING & BENDING-2\": \"70\",\n", "                                            \"HARDENING & TEMPERING\": \"80\"\n", "                                        }\n", "                                    },\n", "                                    \"BALL END-1\": {\n", "                                        \"Processes\": {\n", "                                            \"BOUGHT OUT PART (FORGING)\": \"90a\",\n", "                                            \"ROUGH DRILLING\": \"90\",\n", "                                            \"COPY TURNING\": \"100\",\n", "                                            \"TAPER TURNING\": \"110\"\n", "                                        }\n", "                                    }\n", "                                }\n", "                            },\n", "\n", "                    \"\"\"\n", "        ])\n", "       \n", "        # Clean up uploaded file\n", "        genai.delete_file(uploaded_file.name)\n", "       \n", "        if response.text:\n", "            return response.text.strip()\n", "        else:\n", "            return \"No text extracted\"\n", "           \n", "    except Exception as e:\n", "        print(f\"Error: {e}\")\n", "        return None\n", "    \n", "\n", "#--------------------------------------------------Extracting data of Process Flow Diagram-----------------------------------------\n", "\n", "pfd = extract_text_simple_gemini_pfd(r\"C:\\Users\\<USER>\\Desktop\\swaraj\\files\\PFD - P80009246A.pdf\", GEMINI_API_KEY)\n", "pfd_result = extract_and_parse_json(pfd)\n", "pfd_result"]}, {"cell_type": "code", "execution_count": 119, "id": "56fe7a5e", "metadata": {}, "outputs": [], "source": ["# Example 1:\n", "\n", "# Input:\n", "# Supplier Name: ABC INDUSTRIES  \n", "# Supplier Code: XY123456Z  \n", "# Part Number: ABC1234  \n", "# Part Name: CONTROL ARM  \n", "# Latest Alteration: B,002;15.04.2023  \n", "\n", "# Child Parts and Processes:\n", "# - SHAFT: (a)\n", "#   1. CUTTING - 10\n", "#   2. TURNING - 20\n", "#   3. <PERSON><PERSON><PERSON> - 30\n", "\n", "# - NUT: (b)\n", "#   1. FORGING - 40a\n", "#   2. THREADING - 50\n", "\n", "# - FINAL ASSY: (c)\n", "#   1. SHAFT + NUT WELDING - 60\n", "#   2. PAINTING - 70\n", "\n", "# Output:\n", "# {\n", "#   \"Supplier Information\": {\n", "#     \"Supplier Name\": \"ABC INDUSTRIES\",\n", "#     \"Supplier Code\": \"XY123456Z\",\n", "#     \"Part Number\": \"ABC1234\",\n", "#     \"Part Name\": \"CONTROL ARM\",\n", "#     \"Latest Alteration\": \"B,002;15.04.2023\"\n", "#   },\n", "#   \"Child Parts and Processes\": {\n", "#     \"SHAFT\": {\n", "#       \"Part Type\": \"(a)\",\n", "#       \"Processes\": {\n", "#         \"CUTTING\": \"10\",\n", "#         \"TURNING\": \"20\",\n", "#         \"MILLING\": \"30\"\n", "#       }\n", "#     },\n", "#     \"NUT\": {\n", "#       \"Part Type\": \"(b)\",\n", "#       \"Processes\": {\n", "#         \"FORGING\": \"40a\",\n", "#         \"THREADING\": \"50\"\n", "#       }\n", "#     },\n", "#     \"FINAL ASSY\": {\n", "#       \"Part Type\": \"(c)\",\n", "#       \"Processes\": {\n", "#         \"SHAFT + NUT WELDING\": \"60\",\n", "#         \"PAINTING\": \"70\"\n", "#       }\n", "#     }\n", "#   }\n", "# }"]}, {"cell_type": "markdown", "id": "38d00598", "metadata": {}, "source": ["# PFMEA"]}, {"cell_type": "code", "execution_count": 120, "id": "16938272", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["P80009246A\n", "P80009246A\n", "P80009246A\n", "P80009246A\n", "LOWER LINK ASSY.\n", "LOWER LINK ASSY.\n", "LOWER LINK ASSY.\n", "LOWER LINK ASSY.\n", "FLAT\n", "\n", "BALL END-P80009101A\n", "\n", "BALL END CAT - P80009247A\n", "\n", "BALL CAT 1 - P280801\n", "\n", "LOWER LINK (SUB ASSY.)\n", "\n", "LOWER LINK ASSY.\n", "\n", "12\n", "['S.NO', 'PROCESS FUNCTIONAL REQUIRMENT', 'POTENTIAL FAILURE MODE', 'POTENTIAL EFFECT MODE', 'S E V', 'POTENTIAL CAUSES', 'CURRENT PROCESS CONTROLS PREVENTION', '<PERSON>CC<PERSON>', 'CURRENT PROCESS CONTROLS DETECTION', 'DETEC', 'R     P    N', 'RECOMMENDED ACTION', 'RESPONSIBILITY & TARGET DATE', 'ACTION TAKEN', 'SEV', 'OCC<PERSON>', 'DETEC', 'RPN']\n", "FLAT\n", "12\n", "   S.NO PROCESS FUNCTIONAL REQUIRMENT    POTENTIAL FAILURE MODE  \\\n", "13   10                       CUTTING             Excess Length   \n", "14   10                       CUTTING             Short length    \n", "15   20     1st SIDE CHAMFER GRINDING              CHAMFER U/S    \n", "16   20     1st SIDE CHAMFER GRINDING               CHAMFER O/S   \n", "17   30     2nd SIDE CHAMFER GRINDING              CHAMFER U/S    \n", "18   30     2nd SIDE CHAMFER GRINDING               CHAMFER O/S   \n", "19   40                  VMC DRILLING                  Dia u/s    \n", "20   40                  VMC DRILLING                  Dia o/s    \n", "21   50                    CHAMFERING              CHAMFER DEEP   \n", "22   60     PRE-HEATING & BENDING - 1  Not properly pre-heating   \n", "23   70     PRE-HEATING & BENDING - 2  Not properly pre-heating   \n", "24   80       \\nHARDENING & TEMPERING             Less Hardness   \n", "25   80       \\nHARDENING & TEMPERING           Excess Hardness   \n", "\n", "   POTENTIAL EFFECT MODE S E V  \\\n", "13                Rework     3   \n", "14             Rejection     5   \n", "15                Rework     3   \n", "16             Rejection     5   \n", "17                Rework     3   \n", "18             Rejection     5   \n", "19                Rework     4   \n", "20             Rejection     5   \n", "21             Rejection     5   \n", "22    Bending not proper     4   \n", "23    Bending not proper     4   \n", "24             Less Life     6   \n", "25      Piece may Damage     6   \n", "\n", "                                     POTENTIAL CAUSES  \\\n", "13                              Wrong stopper setting   \n", "14                              Wrong stopper setting   \n", "15                  Templete not provide to operation   \n", "16                  Templete not provide to operation   \n", "17                  Templete not provide to operation   \n", "18                  Templete not provide to operation   \n", "19         Programme & drill not as per required size   \n", "20         Programme & drill not as per required size   \n", "21    Wrong Chamfer tool Selection, Sharpness problem   \n", "22  Less time taken during                pre-heating   \n", "23  Less time taken during                pre-heating   \n", "24  complete cycletime not given,Less qty of fuel ...   \n", "25        excess time is given,more qty of fuel flow,   \n", "\n", "                  CURRENT PROCESS CONTROLS PREVENTION OCCUR  \\\n", "13         Run the m/c after clearence from inspector     3   \n", "14         Run the m/c after clearence from inspector     3   \n", "15  Run the m/c after clearence from inspector,che...     3   \n", "16  Run the m/c after clearence from inspector,che...     3   \n", "17  Run the m/c after clearence from inspector,che...     3   \n", "18  Run the m/c after clearence from inspector,che...     3   \n", "19  Run the m/c after clearence from inspector,che...     3   \n", "20  Run the m/c after clearence from inspector,che...     3   \n", "21  Checked the chamfer tool from forman/inspector...     8   \n", "22         Less time gap during pre-heating & bending     3   \n", "23         Less time gap during pre-heating & bending     3   \n", "24  operator training,qty of fuel to be flow must ...     6   \n", "25  operator training,qty of fuel to be flow must ...     6   \n", "\n", "   CURRENT PROCESS CONTROLS DETECTION DETEC R     P    N  \\\n", "13                  Dimension measure     3           27   \n", "14             MARKING WITH RED PAINT     3           45   \n", "15                  Dimension measure     3           27   \n", "16             MARKING WITH RED PAINT     3           45   \n", "17                  Dimension measure     3           27   \n", "18             MARKING WITH RED PAINT     3           45   \n", "19                  Dimension measure     3           36   \n", "20                  Dimension measure     3           45   \n", "21                   Checked visually     2           80   \n", "22                  Dimension measure     3           36   \n", "23                  Dimension measure     3           36   \n", "24            <PERSON>well Hardner Tester     3          108   \n", "25            <PERSON>well Hardner Tester     3          108   \n", "\n", "                                   RECOMMENDED ACTION  \\\n", "13     Operator training, Provide gauge tio Operator.   \n", "14     Operator training, Provide gauge tio Operator.   \n", "15     Operator training, Provide gauge tio Operator.   \n", "16     Operator training, Provide gauge tio Operator.   \n", "17     Operator training, Provide gauge tio Operator.   \n", "18     Operator training, Provide gauge tio Operator.   \n", "19                                  Operator training   \n", "20                                  Operator training   \n", "21           Setting Approval Slip may be implemented   \n", "22  operator training, Check the proper red ness a...   \n", "23  operator training, Check the proper red ness a...   \n", "24  operator training,qty of fuel to be flow must ...   \n", "25  operator training,qty of fuel to be flow must ...   \n", "\n", "                         RESPONSIBILITY & TARGET DATE  \\\n", "13  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "14  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "15  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "16  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "17  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "18  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "19  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "20  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "21  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "22  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "23  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "24  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "25  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "\n", "                                         ACTION TAKEN SEV OCCUR DETEC RPN  \n", "13                        GAUGE GIVEN TO THE OPERATOR   2     2     3  12  \n", "14                        GAUGE GIVEN TO THE OPERATOR   2     2     3  12  \n", "15                     TEMPLETE GIVEN TO THE OPERATOR   2     2     3  12  \n", "16                         TEMPLETE GIVEN TO OPERATOR   2     2     3  12  \n", "17                     TEMPLETE GIVEN TO THE OPERATOR   2     2     3  12  \n", "18                         TEMPLETE GIVEN TO OPERATOR   2     2     3  12  \n", "19                                  Operator training   4     2     3  24  \n", "20                                  Operator training   5     2     3  30  \n", "21                         TRAINING GIVEN TO OPERATOR   3     2     2  12  \n", "22  DIE MUST BE INSPECTED BEFORE GIVEN INTO THE PR...   2     2     3  12  \n", "23  DIE MUST BE INSPECTED BEFORE GIVEN INTO THE PR...   2     2     3  12  \n", "24  work instruction display in the heat-treatment...   2     2     3  12  \n", "25  work instruction display in the heat-treatment...   2     3     3  18  \n", "BALL END-P80009101A\n", "26\n", "     S.NO PROCESS FUNCTIONAL REQUIRMENT POTENTIAL FAILURE MODE  \\\n", "27   90-a               B.O.P (FORGING)        B.O.P (FORGING)   \n", "28     90                ROUGH DRILLING              BORE O/S    \n", "29     90                ROUGH DRILLING              BORE U/S    \n", "30    100                  COPY TURNING               Bore o/s   \n", "31    100                  COPY TURNING               Bore u/s   \n", "32  110-b                 TAPER TURNING            Chamfer o/s   \n", "33  110-b                 TAPER TURNING            Chamfer u/s   \n", "\n", "   POTENTIAL EFFECT MODE S E V  \\\n", "27       B.O.P (FORGING)  None   \n", "28             Rejection     5   \n", "29                Rework     4   \n", "30             Rejection     5   \n", "31                Rework     4   \n", "32             Rejection     4   \n", "33                Rework     3   \n", "\n", "                                     POTENTIAL CAUSES  \\\n", "27                INCOMING MATERIAL INSPECTION REPORT   \n", "28  Wrong drill Selection, Sharpness problem,Fixtu...   \n", "29             Wrong drill Selection,Sharpness damage   \n", "30                           Carelessness of operator   \n", "31  Carelessness of operator, tool sharpness may d...   \n", "32                                    Setting Poblem    \n", "33                 Master sample not as per req. size   \n", "\n", "                  CURRENT PROCESS CONTROLS PREVENTION  \\\n", "27                INCOMING MATERIAL INSPECTION REPORT   \n", "28  Checked the drill size by putting into the bus...   \n", "29                   check the first piece with gauge   \n", "30         Run the m/c after clearence from inspector   \n", "31  Run the m/c after clearence from inspector, Ch...   \n", "32         Run the m/c after clearence from inspector   \n", "33  Run the m/c after clearence from inspector, Ch...   \n", "\n", "                                  OCCUR   CURRENT PROCESS CONTROLS DETECTION  \\\n", "27  INCOMING MATERIAL INSPECTION REPORT  INCOMING MATERIAL INSPECTION REPORT   \n", "28                                    4                   Checked with gauge   \n", "29                                    6                   Checked with gauge   \n", "30                                    4                           plug gauge   \n", "31                                    4                           plug gauge   \n", "32                                    4               MARKING WITH RED PAINT   \n", "33                                    2                    Dimension measure   \n", "\n", "                                  DETEC                         R     P    N  \\\n", "27  INCOMING MATERIAL INSPECTION REPORT  INCOMING MATERIAL INSPECTION REPORT   \n", "28                                    3                                   60   \n", "29                                    3                                   72   \n", "30                                    3                                   60   \n", "31                                    3                                   48   \n", "32                                    3                                   48   \n", "33                                    3                                   18   \n", "\n", "                                   RECOMMENDED ACTION  \\\n", "27                INCOMING MATERIAL INSPECTION REPORT   \n", "28  Gauge given to the operator,Setting Approval S...   \n", "29  Gauge given to the operator,Setting Approval S...   \n", "30  Gauge given to the operator,Setting Approval S...   \n", "31  Gauge given to the operator,Setting Approval S...   \n", "32  Gauge given to the operator,Setting Approval S...   \n", "33  Gauge given to the operator,Setting Approval S...   \n", "\n", "                         RESPONSIBILITY & TARGET DATE  \\\n", "27                INCOMING MATERIAL INSPECTION REPORT   \n", "28  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "29  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "30  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "31  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "32  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "33  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "\n", "                           ACTION TAKEN                                  SEV  \\\n", "27  INCOMING MATERIAL INSPECTION REPORT  INCOMING MATERIAL INSPECTION REPORT   \n", "28              GAUGE GIVEN TO OPERATOR                                    5   \n", "29              GAUGE GIVEN TO OPERATOR                                    4   \n", "30              GAUGE GIVEN TO OPERATOR                                    5   \n", "31              GAUGE GIVEN TO OPERATOR                                    4   \n", "32           SOP display in the machine                                    4   \n", "33           SOP display in the machine                                    3   \n", "\n", "                                  OCCUR                                DETEC  \\\n", "27  INCOMING MATERIAL INSPECTION REPORT  INCOMING MATERIAL INSPECTION REPORT   \n", "28                                    2                                    3   \n", "29                                    2                                    3   \n", "30                                    2                                    3   \n", "31                                    2                                    3   \n", "32                                    2                                    3   \n", "33                                    2                                    3   \n", "\n", "                                    RPN  \n", "27  INCOMING MATERIAL INSPECTION REPORT  \n", "28                                   30  \n", "29                                   24  \n", "30                                   30  \n", "31                                   24  \n", "32                                   24  \n", "33                                   18  \n", "BALL END CAT - P80009247A\n", "34\n", "     S.NO PROCESS FUNCTIONAL REQUIRMENT POTENTIAL FAILURE MODE  \\\n", "35  120-A               <PERSON>.O.P (FORGING)        B.O.P (FORGING)   \n", "36    120                ROUGH DRILLING              BORE O/S    \n", "37    120                ROUGH DRILLING              BORE U/S    \n", "38    130                  COPY TURNING               Bore o/s   \n", "39    130                  COPY TURNING               Bore u/s   \n", "40    140                 TAPER TURNING            Chamfer o/s   \n", "41    140                 TAPER TURNING            Chamfer u/s   \n", "\n", "   POTENTIAL EFFECT MODE S E V  \\\n", "35       B.O.P (FORGING)  None   \n", "36             Rejection     5   \n", "37                Rework     4   \n", "38             Rejection     5   \n", "39                Rework     4   \n", "40             Rejection     4   \n", "41                Rework     3   \n", "\n", "                                     POTENTIAL CAUSES  \\\n", "35                INCOMING MATERIAL INSPECTION REPORT   \n", "36  Wrong drill Selection, Sharpness problem,Fixtu...   \n", "37             Wrong drill Selection,Sharpness damage   \n", "38                           Carelessness of operator   \n", "39  Carelessness of operator, tool sharpness may d...   \n", "40                                    Setting Poblem    \n", "41                 Master sample not as per req. size   \n", "\n", "                  CURRENT PROCESS CONTROLS PREVENTION  \\\n", "35                INCOMING MATERIAL INSPECTION REPORT   \n", "36  Checked the drill size by putting into the bus...   \n", "37                   check the first piece with gauge   \n", "38         Run the m/c after clearence from inspector   \n", "39  Run the m/c after clearence from inspector, Ch...   \n", "40         Run the m/c after clearence from inspector   \n", "41  Run the m/c after clearence from inspector, Ch...   \n", "\n", "                                  OCCUR   CURRENT PROCESS CONTROLS DETECTION  \\\n", "35  INCOMING MATERIAL INSPECTION REPORT  INCOMING MATERIAL INSPECTION REPORT   \n", "36                                    4                   Checked with gauge   \n", "37                                    6                   Checked with gauge   \n", "38                                    4                           plug gauge   \n", "39                                    4                           plug gauge   \n", "40                                    4               MARKING WITH RED PAINT   \n", "41                                    2                    Dimension measure   \n", "\n", "                                  DETEC                         R     P    N  \\\n", "35  INCOMING MATERIAL INSPECTION REPORT  INCOMING MATERIAL INSPECTION REPORT   \n", "36                                    3                                   60   \n", "37                                    3                                   72   \n", "38                                    3                                   60   \n", "39                                    3                                   48   \n", "40                                    3                                   48   \n", "41                                    3                                   18   \n", "\n", "                                   RECOMMENDED ACTION  \\\n", "35                INCOMING MATERIAL INSPECTION REPORT   \n", "36  Gauge given to the operator,Setting Approval S...   \n", "37  Gauge given to the operator,Setting Approval S...   \n", "38  Gauge given to the operator,Setting Approval S...   \n", "39  Gauge given to the operator,Setting Approval S...   \n", "40  Gauge given to the operator,Setting Approval S...   \n", "41  Gauge given to the operator,Setting Approval S...   \n", "\n", "                         RESPONSIBILITY & TARGET DATE  \\\n", "35                INCOMING MATERIAL INSPECTION REPORT   \n", "36  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "37  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "38  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "39  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "40  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "41  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "\n", "                           ACTION TAKEN                                  SEV  \\\n", "35  INCOMING MATERIAL INSPECTION REPORT  INCOMING MATERIAL INSPECTION REPORT   \n", "36              GAUGE GIVEN TO OPERATOR                                    5   \n", "37              GAUGE GIVEN TO OPERATOR                                    4   \n", "38              GAUGE GIVEN TO OPERATOR                                    5   \n", "39              GAUGE GIVEN TO OPERATOR                                    4   \n", "40           SOP display in the machine                                    4   \n", "41           SOP display in the machine                                    3   \n", "\n", "                                  OCCUR                                DETEC  \\\n", "35  INCOMING MATERIAL INSPECTION REPORT  INCOMING MATERIAL INSPECTION REPORT   \n", "36                                    2                                    3   \n", "37                                    2                                    3   \n", "38                                    2                                    3   \n", "39                                    2                                    3   \n", "40                                    2                                    3   \n", "41                                    2                                    3   \n", "\n", "                                    RPN  \n", "35  INCOMING MATERIAL INSPECTION REPORT  \n", "36                                   30  \n", "37                                   24  \n", "38                                   30  \n", "39                                   24  \n", "40                                   24  \n", "41                                   18  \n", "BALL CAT 1 - P280801\n", "42\n", "     S.NO PROCESS FUNCTIONAL REQUIRMENT   POTENTIAL FAILURE MODE  \\\n", "43  150-a                         B.O.P                    B.O.P   \n", "44    150         HARDENING & TEMPERING            Less Hardness   \n", "45    150         HARDENING & TEMPERING          Excess Hardness   \n", "46    160                 SHOT BLASTING        Not properly Done   \n", "47    170                    Zn PLATING  Less thk & poor plating   \n", "\n", "                         POTENTIAL EFFECT MODE S E V  \\\n", "43                                       B.O.P  None   \n", "44                                   Less Life     6   \n", "45                            Piece may Damage     6   \n", "46  Primer not properly adhesive with material     4   \n", "47                                      Rework     2   \n", "\n", "                                     POTENTIAL CAUSES  \\\n", "43                INCOMING MATERIAL INSPECTION REPORT   \n", "44  complete cycletime not given,Less qty of fuel ...   \n", "45        excess time is given,more qty of fuel flow,   \n", "46  Less shot in the m/c,complete cycle time not g...   \n", "47                          carelessness of operator    \n", "\n", "                  CURRENT PROCESS CONTROLS PREVENTION  \\\n", "43                INCOMING MATERIAL INSPECTION REPORT   \n", "44  operator training,qty of fuel to be flow must ...   \n", "45  operator training,qty of fuel to be flow must ...   \n", "46                                  operator training   \n", "47                                  operator training   \n", "\n", "                                  OCCUR   CURRENT PROCESS CONTROLS DETECTION  \\\n", "43  INCOMING MATERIAL INSPECTION REPORT  INCOMING MATERIAL INSPECTION REPORT   \n", "44                                    6              <PERSON>well Hardner Tester   \n", "45                                    6              <PERSON><PERSON> Hardner Tester   \n", "46                                    3                             visually   \n", "47                                    3                           coat gauge   \n", "\n", "                                  DETEC                         R     P    N  \\\n", "43  INCOMING MATERIAL INSPECTION REPORT  INCOMING MATERIAL INSPECTION REPORT   \n", "44                                    3                                  108   \n", "45                                    3                                  108   \n", "46                                    3                                   36   \n", "47                                    2                                   12   \n", "\n", "                                   RECOMMENDED ACTION  \\\n", "43                INCOMING MATERIAL INSPECTION REPORT   \n", "44  operator training,qty of fuel to be flow must ...   \n", "45  operator training,qty of fuel to be flow must ...   \n", "46  operator training,position of shots in the mac...   \n", "47     Operator training, chart displayed in the shop   \n", "\n", "                         RESPONSIBILITY & TARGET DATE  \\\n", "43                INCOMING MATERIAL INSPECTION REPORT   \n", "44  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "45  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "46  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "47  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "\n", "                                         ACTION TAKEN  \\\n", "43                INCOMING MATERIAL INSPECTION REPORT   \n", "44  work instruction display in the heat-treatment...   \n", "45  work instruction display in the heat-treatment...   \n", "46  shot position checked at the start  of shift,t...   \n", "47            work instruction display in the section   \n", "\n", "                                    SEV                                OCCUR  \\\n", "43  INCOMING MATERIAL INSPECTION REPORT  INCOMING MATERIAL INSPECTION REPORT   \n", "44                                    2                                    2   \n", "45                                    2                                    3   \n", "46                                    2                                    2   \n", "47                                    2                                    2   \n", "\n", "                                  DETEC                                  RPN  \n", "43  INCOMING MATERIAL INSPECTION REPORT  INCOMING MATERIAL INSPECTION REPORT  \n", "44                                    3                                   12  \n", "45                                    3                                   18  \n", "46                                    2                                    8  \n", "47                                    2                                    8  \n", "LOWER LINK (SUB ASSY.)\n", "48\n", "   S.NO   PROCESS FUNCTIONAL REQUIRMENT   POTENTIAL FAILURE MODE  \\\n", "49  180  PART a, b, c  (TACK & WELDING)          Material Burns    \n", "50  180  PART a, b, c  (TACK & WELDING)  Ball end tapper welding   \n", "51  180  PART a, b, c  (TACK & WELDING)       Overflow material    \n", "52  190                       INDUCTION      HEAT MORE THEN REQ.   \n", "53  200                   SHOT BLASTING        Not properly Done   \n", "54  210  PRIMER COATING\\n(GREY PRIMER)         Less thk of paint   \n", "55  210  PRIMER COATING\\n(GREY PRIMER)         More thk of paint   \n", "\n", "                         POTENTIAL EFFECT MODE S E V  \\\n", "49                              Material scrap     6   \n", "50                                      Rework     4   \n", "51                           Improper welding      5   \n", "52                             <PERSON> Burns   None   \n", "53  Primer not properly adhesive with material     4   \n", "54     Material start rusting after some times     4   \n", "55                 Paint not properly adhesive     4   \n", "\n", "                                     POTENTIAL CAUSES  \\\n", "49                                     Excess current   \n", "50                               Opearor negligiency    \n", "51           1.) Excess wire speed\\n2.)Torch setting    \n", "52                              Excess current & HEAT   \n", "53  Less shot in the m/c,complete cycle time not g...   \n", "54             Mixture of paint not in required ratio   \n", "55  Mixture of paint not in required ratio, Double...   \n", "\n", "                  CURRENT PROCESS CONTROLS PREVENTION OCCUR  \\\n", "49  Current setting table acc. to  thk of material...     8   \n", "50                                  Operator training     4   \n", "51  1.) Wire speed according to flat & Ball end th...     6   \n", "52  Current setting table acc. to  thk of material...  None   \n", "53                                  operator training     3   \n", "54                                  Operator training     3   \n", "55                                  Operator training     3   \n", "\n", "   CURRENT PROCESS CONTROLS DETECTION DETEC R     P    N  \\\n", "49                           Visually     3          144   \n", "50                       Right angle      3           48   \n", "51                           Visually     3           90   \n", "52                           Visually     1           44   \n", "53                           visually     3           36   \n", "54                           Cup Test    10          120   \n", "55                 Cup Test,Tape Test    10          120   \n", "\n", "                                   RECOMMENDED ACTION  \\\n", "49  Current setting table acc. to  thk of material...   \n", "50                               Rework the material    \n", "51        WIRE SPEED MUST BE SET ACCORDING TO THE JOB   \n", "52  Current setting table acc. to  thk of material...   \n", "53  operator training,position of shots in the mac...   \n", "54  Operator training,ratio mixture chart displaye...   \n", "55  Operator training,ratio mixture chart displaye...   \n", "\n", "                         RESPONSIBILITY & TARGET DATE  \\\n", "49  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "50  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "51  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "52  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "53  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "54  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "55  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "\n", "                                         ACTION TAKEN SEV OCCUR DETEC RPN  \n", "49                            DISPLAY THE M/C SETTING   3     3     2  18  \n", "50                                  Operator training   3     2     2  12  \n", "51             Wire Speed chart displayon the machine   2     2     2   8  \n", "52                            DISPLAY THE M/C SETTING   2     2     2   8  \n", "53  shot position checked at the start  of shift,t...   2     2     2   8  \n", "54             Ratio list display in painting section   2     2     3  12  \n", "55             Ratio list display in painting section   2     2     3  12  \n", "LOWER LINK ASSY.\n", "56\n", "   S.NO   PROCESS FUNCTIONAL REQUIRMENT POTENTIAL FAILURE MODE  \\\n", "57  220  PART  d, e     (BALL CRIMPING)               Ball jam   \n", "58  220  PART  d, e     (BALL CRIMPING)         Ball play obs,   \n", "59  230   TRACEBILITY CODE   (PUNCHING)        PUNCHING NOT OK   \n", "\n", "   POTENTIAL EFFECT MODE S E V                      POTENTIAL CAUSES  \\\n", "57                Rework     3                  Wrong die setting,     \n", "58                Rework     3  Wrong setting,  limit switch problem   \n", "59               REWORK      4              PRESSURE NOT AS PER REQ.   \n", "\n", "           CURRENT PROCESS CONTROLS PREVENTION OCCUR  \\\n", "57  Run the m/c after clearence from inspector     4   \n", "58  Run the m/c after clearence from inspector     4   \n", "59                   WORK INSTRUCTION DISPAYED     3   \n", "\n", "                  CURRENT PROCESS CONTROLS DETECTION DETEC R     P    N  \\\n", "57  With pin,     hyd. Pressure gauge of the machine     3           36   \n", "58   With pin,    hyd. Pressure gauge of the machine     3           36   \n", "59                                          VISUALLY     3           36   \n", "\n", "                                   RECOMMENDED ACTION  \\\n", "57  PRESSURE FOR BALL PRESSING MUST BE DISPLAY ON ...   \n", "58  PRESSURE FOR BALL PRESSING MUST BE DISPLAY ON ...   \n", "59                                   CHECKED VISUALLY   \n", "\n", "                         RESPONSIBILITY & TARGET DATE  \\\n", "57  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "58  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "59  PRODUCTION & QUALITY SUPERVISOR BEFORE PRODUCT...   \n", "\n", "                                   ACTION TAKEN SEV OCCUR DETEC RPN  \n", "57  work instruction display in the PRESS SHOP    3     2     3  18  \n", "58  work instruction display in the PRESS SHOP    3     2     3  18  \n", "59       INSTRUCTION DISPAY IN ASSEMBLY SECTION   2     2     2   8  \n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_28708\\1808927498.py:53: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`\n", "  if re.search(pattern, str(row[0])):  # check match in first column\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_28708\\1808927498.py:54: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`\n", "  print(row[1])\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_28708\\1808927498.py:56: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`\n", "  pfmea_validation_criteria[\"bom_child_list\"][row[1]]=index\n"]}], "source": ["# --------------------------------------------------Extraction of PFMEA Dataframe---------------------------------------\n", "from openpyxl import load_workbook\n", "import pandas as pd\n", "\n", "# ✅ Load the workbook with data_only=True to get computed values instead of formulas\n", "wb = load_workbook(\"PFMEA LOWER LINK.xlsx\", data_only=True)\n", "ws = wb.active  # Or use wb[\"SheetName\"] if needed\n", "\n", "# Handle merged cells\n", "data = []\n", "for row in ws.iter_rows():\n", "    row_data = []\n", "    for cell in row:\n", "        if cell.coordinate in ws.merged_cells:\n", "            # If it's a merged cell, get value from the top-left cell of the merged range\n", "            for merged_range in ws.merged_cells.ranges:\n", "                if cell.coordinate in merged_range:\n", "                    cell = ws.cell(merged_range.min_row, merged_range.min_col)\n", "                    break\n", "        row_data.append(cell.value)\n", "    data.append(row_data)\n", "\n", "# Create DataFrame\n", "pfmea_df = pd.DataFrame(data)\n", "\n", "# Set first row as header\n", "pfmea_df.columns = pfmea_df.iloc[0]\n", "pfmea_df = pfmea_df.drop(index=0).reset_index(drop=True)\n", "\n", "# --------------------------------------------------Creating PFMEA Json---------------------------------------\n", "\n", "pfmea_validation_criteria = {\n", "    \"part_info\": {\n", "        \"part_number\": \"\",\n", "        \"item_name\": \"\"\n", "    },\n", "    \"bom_child_list\": {},\n", "    \"dataframe\": {},\n", "}\n", "\n", "\n", "# --------------------------------------------------Extracting Part Number and Item Name---------------------------------------\n", "import re\n", "for index, row in pfmea_df.iterrows():\n", "    for j in row:\n", "        if \"Part No\" in str(j):\n", "            print(str(j).split(\" \")[-1])\n", "            pfmea_validation_criteria[\"part_info\"][\"part_number\"] = str(j).split(\" \")[-1]\n", "        if 'Item Name' in str(j):\n", "            print(str(j).split(\":  \")[-1])\n", "            pfmea_validation_criteria[\"part_info\"][\"item_name\"] = str(j).split(\":  \")[-1]\n", "    pattern = r\"^\\([a-z]\\)$\"\n", "    if re.search(pattern, str(row[0])):  # check match in first column\n", "        print(row[1]) \n", "        print() # print second column\n", "        pfmea_validation_criteria[\"bom_child_list\"][row[1]]=index\n", "\n", "# --------------------------------------------------Extracting Column Names---------------------------------------\n", "\n", "first_key = list(pfmea_validation_criteria['bom_child_list'])[0]\n", "print(pfmea_validation_criteria['bom_child_list'][first_key])\n", "column_no = pfmea_validation_criteria['bom_child_list'][first_key]-1\n", "column = pfmea_df.iloc[column_no].tolist()[:18]  # ✅ Use .iloc\n", "print(column)\n", "\n", "\n", "# --------------------------------------------------Extracting Dataframes---------------------------------------\n", "\n", "\n", "com_child_list =list(pfmea_validation_criteria['bom_child_list'])\n", "for i in range(len(com_child_list)):\n", "    print(com_child_list[i])\n", "    print(pfmea_validation_criteria['bom_child_list'][com_child_list[i]])\n", "    if i == len(com_child_list)-1:\n", "        temp_df = pfmea_df.iloc[pfmea_validation_criteria['bom_child_list'][com_child_list[i]]+1:pfmea_validation_criteria['bom_child_list'][com_child_list[i]]+4,:18]\n", "        # break\n", "    else:\n", "        temp_df = pfmea_df.iloc[pfmea_validation_criteria['bom_child_list'][com_child_list[i]]+1:pfmea_validation_criteria['bom_child_list'][com_child_list[i+1]],:18]\n", "    temp_df.columns = column\n", "    pfmea_validation_criteria['dataframe'][com_child_list[i]] = temp_df\n", "    print(temp_df)\n", "    \n"]}, {"cell_type": "markdown", "id": "a6f77c5b", "metadata": {}, "source": ["# Control Plan"]}, {"cell_type": "code", "execution_count": 121, "id": "6f338c94", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["P80009246A\n", "P80009246A\n", "P80009246A\n", "P80009246A\n", "LOWER LINK ASSY.\n", "LOWER LINK ASSY.\n", "LOWER LINK ASSY.\n", "LOWER LINK ASSY.\n", "FLAT\n", "\n", "BALL END-P80009101A\n", "\n", "BALL END CAT - P80009247A\n", "\n", "BALL CAT 1 - P280801\n", "\n", "LOWER LINK (SUB ASSY.)\n", "\n", "LOWER LINK ASSY.\n", "\n", "8\n", "['Part / Process Number.', 'Process Name / Operation Description.', 'Machine Device Jig <PERSON>l For Mfg.', 'Product.', 'Process.', 'Special Character Class.', 'Product/Process Specification.', 'Evaluation Measurement Technique.', 'Size.', 'Frequency.', 'Control Method.', 'Responsibility', 'Reaction Plan & Corrective action']\n", "FLAT\n", "8\n", "   Part / Process Number. Process Name / Operation Description.  \\\n", "9                    10-A              RAW MATERIAL INSPECTION    \n", "10                     10                               CUTTING   \n", "11                     20             1st SIDE CHAMFER GRINDING   \n", "12                     30             2nd SIDE CHAMFER GRINDING   \n", "13                     40                          VMC DRILLING   \n", "14                     50                            CHAMFERING   \n", "15                     60             PRE-HEATING & BENDING - 1   \n", "16                     70                           BENDING - 2   \n", "17                     80                 HARDENING & TEMPERING   \n", "\n", "   Machine Device Jig Tool For Mfg.            Product.     Process.  \\\n", "9          RAW MATERIAL INSPECTION           WIDTH, THK         None   \n", "10              PROFILE CUTTING M/C  LENGTH, WIDTH, THK         None   \n", "11                    BENCH G<PERSON>NDER        CHAMFER SIZE         None   \n", "12                    BENCH GRINDER        CHAMFER SIZE         None   \n", "13                              VMC        HOLE DIA, CD         None   \n", "14                        DRILL M/C        CHAMFER SIZE         None   \n", "15                      HYD. PRESS          DIM,\\nANGLE         None   \n", "16                      HYD. PRESS          DIM,\\nANGLE         None   \n", "17                              GCF            HARDNESS  TEMPERATURE   \n", "\n", "   Special Character Class.  \\\n", "9                      None   \n", "10                     None   \n", "11                     None   \n", "12                     None   \n", "13                     None   \n", "14                     None   \n", "15                     None   \n", "16                     None   \n", "17         MICRO  STRUCTURE   \n", "\n", "                       Product/Process Specification.  \\\n", "9                                   65.00MM , 15.00MM   \n", "10                       464.00MM , 65.00MM , 15.00MM   \n", "11                                              6X45°   \n", "12                                              6X45°   \n", "13  Ø21.20+0.2MM, Ø19.20+0.2MM,\\n50.00±1MM,       ...   \n", "14                                            0.5X45°   \n", "15                                      122.0,\\n3.14°   \n", "16              359.0,\\n436.0,\\n13.00,\\n85.0,\\n39.94°   \n", "17                                        341-415 BHN   \n", "\n", "                    Evaluation Measurement Technique.                 Size.  \\\n", "9                       VERNIER CALIPER                AS PER SAMPLING PLAN   \n", "10  VERNIER CALIPER,                     MEASURING...               2 PCS.    \n", "11                                    BEVEL PROTECTOR               2 PCS.    \n", "12                                    BEVEL PROTECTOR               2 PCS.    \n", "13                      VERNIER CALIPER, HEIGHT GAUGE               2 PCS.    \n", "14                                           VISUALLY               2 PCS.    \n", "15           BEVEL PROTECTOR,\\n HEIGHT GAUGE,                       2 PCS.    \n", "16   MEASURING TAPE,  BEVEL PROTECTOR,\\n HEIGHT GA...               2 PCS.    \n", "17                                     BRINELL TESTER                     2   \n", "\n", "   Frequency.        Control Method.         Responsibility  \\\n", "9     PER LOT       I.I.R\\nFM-QAD-01       INCOMING QUALITY   \n", "10     2 HRS.       I.P.R\\nFM-QAD-03  PRODUCTION SUPERVISOR   \n", "11     2 HRS.       I.P.R\\nFM-QAD-03  PRODUCTION SUPERVISOR   \n", "12     2 HRS.       I.P.R\\nFM-QAD-03  PRODUCTION SUPERVISOR   \n", "13     2 HRS.       I.P.R\\nFM-QAD-03  PRODUCTION SUPERVISOR   \n", "14     2 HRS.       I.P.R\\nFM-QAD-03  PRODUCTION SUPERVISOR   \n", "15     2 HRS.       I.P.R\\nFM-QAD-03  PRODUCTION SUPERVISOR   \n", "16     2 HRS.       I.P.R\\nFM-QAD-03  PRODUCTION SUPERVISOR   \n", "17    PER LOT  LAB REPORT\\nFM-LAB-01           LAB INCHARGE   \n", "\n", "                    Reaction Plan & Corrective action  \n", "9                LESS OR OVER SIZE SEND TO THE VENDOR  \n", "10                                U/S REJ.,O/S REWORK  \n", "11                                   U/S REW.,O/S REJ  \n", "12                                   U/S REW.,O/S REJ  \n", "13  HOLE U/S REW, O/S REJ,                        ...  \n", "14                            VISUALLY NOT OK REWORK   \n", "15                          BENDING NOT PROPER-REWORK  \n", "16                          BENDING NOT PROPER-R<PERSON><PERSON><PERSON>K  \n", "17                       LESS / MORE  HARDNESS REWORK  \n", "BALL END-P80009101A\n", "18\n", "   Part / Process Number. Process Name / Operation Description.  \\\n", "19                   90-a                       BOUGHT OUT PART   \n", "20                     90                        ROUGH DRILLING   \n", "21                    100                          COPY TURNING   \n", "22                    110                         TAPER TURNING   \n", "\n", "                     Machine Device Jig Tool For Mfg.  \\\n", "19  ALL DIM AND PARAMETER SHOULD BE CHECKED DURING...   \n", "20                                     HYD. DRILL M/C   \n", "21                                   COPY TURNING M/C   \n", "22                                   COPY TURNING M/C   \n", "\n", "                                             Product.  \\\n", "19  ALL DIM AND PARAMETER SHOULD BE CHECKED DURING...   \n", "20                                               DIA    \n", "21                                          DIM, DIA    \n", "22                                          DIM, DIA    \n", "\n", "                                             Process.  \\\n", "19  ALL DIM AND PARAMETER SHOULD BE CHECKED DURING...   \n", "20                                               None   \n", "21                                               None   \n", "22                                               None   \n", "\n", "                             Special Character Class.  \\\n", "19  ALL DIM AND PARAMETER SHOULD BE CHECKED DURING...   \n", "20                                               None   \n", "21                                               None   \n", "22                                               None   \n", "\n", "                       Product/Process Specification.  \\\n", "19  ALL DIM AND PARAMETER SHOULD BE CHECKED DURING...   \n", "20                        ø40.00+0.5mm, ø34.00+0.5mm,   \n", "21  Ø44.2+0.2mm, Ø44.2+0.2(SPH.), Ø38.5mm, 26.50 m...   \n", "22                          Ø47.00+0.2mm, 8.50mm, 30°   \n", "\n", "                    Evaluation Measurement Technique.  \\\n", "19  VERNIER CALIPER , RADIUS GAUGE ,   BEVEL PROTE...   \n", "20                                 VERNIER CALIPER,     \n", "21  VERNIER CALIPER      HEIGHT GAUGE,    PLUG GAU...   \n", "22               VERNIER CALIPER      BEVEL PROTECTOR   \n", "\n", "                       Size.                Frequency.   Control Method.  \\\n", "19  AS PER OUR SAMPLING PLAN  AS PER OUR SAMPLING PLAN  I.I.R\\nFM-QAD-01   \n", "20                   2 PCS.                     2 HRS.  I.P.R\\nFM-QAD-03   \n", "21                   2 PCS.                     2 HRS.  I.P.R\\nFM-QAD-03   \n", "22                   2 PCS.                     2 HRS.  I.P.R\\nFM-QAD-03   \n", "\n", "           Responsibility     Reaction Plan & Corrective action  \n", "19       INCOMING QUALITY  LESS OR OVER SIZE SEND TO THE VENDOR  \n", "20  PRODUCTION SUPERVISOR                     O/S REJ , U/S REW  \n", "21  PRODUCTION SUPERVISOR                   U/S REWORK.,O/S REJ  \n", "22  PRODUCTION SUPERVISOR           CHAMFER U/S REWORK.,O/S REJ  \n", "BALL END CAT - P80009247A\n", "23\n", "   Part / Process Number. Process Name / Operation Description.  \\\n", "24                  120-a                       BOUGHT OUT PART   \n", "25                    120                        ROUGH DRILLING   \n", "26                    130                          COPY TURNING   \n", "27                    140                         TAPER TURNING   \n", "\n", "                     Machine Device Jig Tool For Mfg.  \\\n", "24  ALL DIM AND PARAMETER SHOULD BE CHECKED DURING...   \n", "25                                     HYD. DRILL M/C   \n", "26                                   COPY TURNING M/C   \n", "27                                   COPY TURNING M/C   \n", "\n", "                                             Product.  \\\n", "24  ALL DIM AND PARAMETER SHOULD BE CHECKED DURING...   \n", "25                                               DIA    \n", "26                                          DIM, DIA    \n", "27                                          DIM, DIA    \n", "\n", "                                             Process.  \\\n", "24  ALL DIM AND PARAMETER SHOULD BE CHECKED DURING...   \n", "25                                               None   \n", "26                                               None   \n", "27                                               None   \n", "\n", "                             Special Character Class.  \\\n", "24  ALL DIM AND PARAMETER SHOULD BE CHECKED DURING...   \n", "25                                               None   \n", "26                                               None   \n", "27                                               None   \n", "\n", "                       Product/Process Specification.  \\\n", "24  ALL DIM AND PARAMETER SHOULD BE CHECKED DURING...   \n", "25                        ø40.00+0.5mm, ø34.00+0.5mm,   \n", "26  Ø44.2+0.2mm, Ø44.2+0.2(SPH.), Ø38.5mm, 26.50 m...   \n", "27                          Ø47.00+0.2mm, 8.50mm, 30°   \n", "\n", "                    Evaluation Measurement Technique.  \\\n", "24  VERNIER CALIPER , RADIUS GAUGE ,   BEVEL PROTE...   \n", "25                                    VERNIER CALIPER   \n", "26  VERNIER CALIPER      HEIGHT GAUGE,    PLUG GAU...   \n", "27               VERNIER CALIPER      BEVEL PROTECTOR   \n", "\n", "                       Size.                Frequency.   Control Method.  \\\n", "24  AS PER OUR SAMPLING PLAN  AS PER OUR SAMPLING PLAN  I.I.R\\nFM-QAD-01   \n", "25                   2 PCS.                     2 HRS.  I.P.R\\nFM-QAD-03   \n", "26                   2 PCS.                     2 HRS.  I.P.R\\nFM-QAD-03   \n", "27                   2 PCS.                     2 HRS.  I.P.R\\nFM-QAD-03   \n", "\n", "           Responsibility     Reaction Plan & Corrective action  \n", "24       INCOMING QUALITY  LESS OR OVER SIZE SEND TO THE VENDOR  \n", "25  PRODUCTION SUPERVISOR                     O/S REJ , U/S REW  \n", "26  PRODUCTION SUPERVISOR                   U/S REWORK.,O/S REJ  \n", "27  PRODUCTION SUPERVISOR           CHAMFER U/S REWORK.,O/S REJ  \n", "BALL CAT 1 - P280801\n", "28\n", "   Part / Process Number.              Process Name / Operation Description.  \\\n", "29                  150-a                                    BOUGHT OUT PART   \n", "30                  150-a                                    BOUGHT OUT PART   \n", "31                    150                              HARDENING & TEMPERING   \n", "32                    160                                      SHOT BLASTING   \n", "33                    170  Zn PLATING     (ELECTRO TRIVALENT CHROMIUM -Cr...   \n", "\n", "   Machine Device Jig Tool For Mfg.                  Product. Process.  \\\n", "29                  BOUGHT OUT PART                 DIM, DIA      None   \n", "30                  BOUGHT OUT PART                  MATERIAL        -   \n", "31                              GCF       HARNESS, CASE DEPTH     None   \n", "32                   SHOT BLAST M/C           PROPER CLEANING     None   \n", "33                     PLATING TANK  PLATING THK, PASSIVATION     None   \n", "\n", "   Special Character Class.                  Product/Process Specification.  \\\n", "29                     None  Ø44.5+0.2MM, \\nØ22.4+0.3MM,\\n35± 0.1MM,\\n1X45˚   \n", "30                     None                                         20MNCR5   \n", "31                     None    HARDNESS= 55-62 HRC\\nCASE DEPTH= 0.4-0.7.5MM   \n", "32                     None      NO OIL, NO RUST,                  NO SCALE   \n", "33                     None   8-12 MICRONS,                TRIVALENT SILVER   \n", "\n", "                    Evaluation Measurement Technique.  \\\n", "29  VERNIER CALIPER , MICRO METER,     VISUALLY,  ...   \n", "30                    MTC, THIRD PARTY SPECTRO REPORT   \n", "31                               ROCKWELL TESTER\\nLAB   \n", "32                                           VISUALLY   \n", "33                            COATING GAUGE, VISUALLY   \n", "\n", "                       Size.                Frequency.        Control Method.  \\\n", "29  AS PER OUR SAMPLING PLAN  AS PER OUR SAMPLING PLAN       I.I.R\\nFM-QAD-01   \n", "30                         1                   PER LOT       I.I.R\\nFM-QAD-01   \n", "31                         2                   PER LOT  LAB REPORT\\nFM-LAB-01   \n", "32                   2 PCS.                     2 HRS.      I.P.R\\nFM-QAD-03B   \n", "33                    5 pcs.           TWO TIMES A DAY      I.P.R\\nFM-QAD-03A   \n", "\n", "           Responsibility                  Reaction Plan & Corrective action  \n", "29       INCOMING QUALITY               LESS OR OVER SIZE SEND TO THE VENDOR  \n", "30           LAB INCHARGE  MATERIAL MISMATCH, REJECT & SEND BACK TO SUPPLIER  \n", "31           LAB INCHARGE                       LESS OR <PERSON><PERSON><PERSON> HARDNESS REWORK  \n", "32  PRODUCTION SUPERVISOR                                  NOT CLEAN  REWORK  \n", "33       PLATING INCHARGE  LESS THK=  REWORK,                            ...  \n", "LOWER LINK (SUB ASSY.)\n", "34\n", "   Part / Process Number.      Process Name / Operation Description.  \\\n", "35                    180  PART a, b, c             (TACK & WELDING)   \n", "36                    180  PART a, b, c             (TACK & WELDING)   \n", "37                    180  PART a, b, c             (TACK & WELDING)   \n", "38                    190                     WELD JOINT NORMALISING   \n", "39                    200                              SHOT BLASTING   \n", "40                    210              PRIMER COATING\\n(GREY COLOUR)   \n", "\n", "   Machine Device Jig Tool For Mfg.              Product.         Process.  \\\n", "35                     MIG WELDING               DIM , CD             None   \n", "36                     MIG WELDING           WELDING BEAD  CURRENT SETTING   \n", "37                     MIG WELDING   WELDING PENETRATION   CURRENT SETTING   \n", "38                    INDUCTION M/C                   HAZ  CURRENT SETTING   \n", "39                   SHOT BLAST M/C       PROPER CLEANING             None   \n", "40                      PAINT BOOTH   PROPER ADHESIVE,THK             None   \n", "\n", "   Special Character Class.  \\\n", "35                     None   \n", "36              PENETRATION   \n", "37              PENETRATION   \n", "38                     None   \n", "39                     None   \n", "40                     None   \n", "\n", "                       Product/Process Specification.  \\\n", "35  570.0 ± 2.0MM, \\n268.0 ± 1.0 mm,\\n204.0 ± 1.0 mm    \n", "36                              WELDING BEAD = 8.00mm   \n", "37                                WELDING PENETRATION   \n", "38                                        450 HV MAX.   \n", "39     NO SCALE, NO RUST,                 NO PITTED,    \n", "40               NOT PEALOFF, NAIL TEST, 30±10microns   \n", "\n", "             Evaluation Measurement Technique.    Size. Frequency.  \\\n", "35                              MEASURING TAPE  2 PCS.      2 HRS.   \n", "36                             VERNIER CALIPER  2 PCS.      2 HRS.   \n", "37                                         LAB        1       None   \n", "38                                         LAB        1       None   \n", "39                                    VISUALLY  2 PCS.      2 HRS.   \n", "40  TAPE TEST,             DFT GAUGE, VISUALLY  2 PCS.      2 HRS.   \n", "\n", "     Control Method.         Responsibility  \\\n", "35  I.P.R\\nFM-QAD-03  PRODUCTION SUPERVISOR   \n", "36  I.P.R\\nFM-QAD-03  PRODUCTION SUPERVISOR   \n", "37              None                   None   \n", "38              None                   None   \n", "39  I.P.R\\nFM-QAD-03  PRODUCTION SUPERVISOR   \n", "40  I.P.R\\nFM-QAD-03  PRODUCTION SUPERVISOR   \n", "\n", "          Reaction Plan & Corrective action  \n", "35                      U/S REJ.,O/S REWORK  \n", "36                 LESS WELDING BEAD=REWORK  \n", "37                  LESS PENETRATION=REJECT  \n", "38                        MORE THEN =REWORK  \n", "39                        NOT CLEAN  REWORK  \n", "40  PEAL OFF THEN REWORK, \\nLESS THK REWORK  \n", "LOWER LINK ASSY.\n", "41\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_28708\\1699108703.py:51: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`\n", "  if re.search(pattern, str(row[0])):  # check match in first column\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_28708\\1699108703.py:52: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`\n", "  print(row[1])\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_28708\\1699108703.py:54: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`\n", "  control_data[\"bom_child_list\"][row[1]]=index\n"]}], "source": ["# --------------------------------------------------Extraction of PFMEA Dataframe---------------------------------------\n", "from openpyxl import load_workbook\n", "\n", "# Load the workbook and sheet\n", "wb = load_workbook(\"15.Control plan .xlsx\" , data_only=True)\n", "ws = wb.active  # or wb[\"Sheet1\"]\n", "\n", "# Create a matrix to store values, handling merged cells\n", "data = []\n", "for row in ws.iter_rows():\n", "    row_data = []\n", "    for cell in row:\n", "        if cell.coordinate in ws.merged_cells:\n", "            # If it's a merged cell, get value from the top-left cell of the merged range\n", "            for merged_range in ws.merged_cells.ranges:\n", "                if cell.coordinate in merged_range:\n", "                    cell = ws.cell(merged_range.min_row, merged_range.min_col)\n", "                    break\n", "        row_data.append(cell.value)\n", "    data.append(row_data)\n", "\n", "\n", "control_df = pd.DataFrame(data)\n", "\n", "# Optionally set the first row as column headers (if needed)\n", "control_df.columns = control_df.iloc[0]\n", "control_df = control_df.drop(index=0).reset_index(drop=True)\n", "\n", "# --------------------------------------------------Creating PFMEA Json---------------------------------------\n", "\n", "control_data = {\n", "    \"part_info\": {\n", "        \"part_number\": \"\",\n", "        \"part_name\": \"\",\n", "        },\n", "    \"bom_child_list\": {},\n", "    \"dataframe\": {},\n", "}\n", "\n", "# --------------------------------------------------Extracting Part Number and Item Name---------------------------------------\n", "import re\n", "for index, row in control_df.iterrows():\n", "    for j in row:\n", "        if \"Part Number\" in str(j):\n", "            print(str(j).split(\". \")[-1])\n", "            control_data[\"part_info\"][\"part_number\"] = str(j).split(\" \")[-1]\n", "        if 'Part Name' in str(j):\n", "            print(str(j).split(\".  \")[-1])\n", "            control_data[\"part_info\"][\"part_name\"] = str(j).split(\".  \")[-1]\n", "    pattern = r\"^\\([a-z]\\)$\"\n", "    if re.search(pattern, str(row[0])):  # check match in first column\n", "        print(row[1]) \n", "        print() # print second column\n", "        control_data[\"bom_child_list\"][row[1]]=index\n", "\n", "# --------------------------------------------------Extracting Column Names---------------------------------------\n", "\n", "first_key = list(control_data['bom_child_list'])[0]\n", "print(control_data['bom_child_list'][first_key])\n", "column_no = control_data['bom_child_list'][first_key]-1\n", "column = control_df.iloc[column_no].tolist()[:13]  # ✅ Use .iloc\n", "column = ['Responsibility' if val == None else val for val in column]\n", "print(column)\n", "\n", "# --------------------------------------------------Extracting Dataframes---------------------------------------\n", "\n", "\n", "com_child_list =list(control_data['bom_child_list'])\n", "for i in range(len(com_child_list)):\n", "    print(com_child_list[i])\n", "    print(control_data['bom_child_list'][com_child_list[i]])\n", "    if i == len(com_child_list)-1:\n", "        temp_df = control_df.iloc[control_data['bom_child_list'][com_child_list[i]]+1:control_data['bom_child_list'][com_child_list[i]]+6,:13]\n", "        break\n", "    else:\n", "        temp_df = control_df.iloc[control_data['bom_child_list'][com_child_list[i]]+1:control_data['bom_child_list'][com_child_list[i+1]],:13]\n", "    temp_df.columns = column\n", "    control_data['dataframe'][com_child_list[i]] = temp_df\n", "    print(temp_df)\n", "    \n"]}, {"cell_type": "markdown", "id": "0919b44d", "metadata": {}, "source": ["# Matching "]}, {"cell_type": "code", "execution_count": 122, "id": "578b6d8a", "metadata": {}, "outputs": [], "source": ["checks={}"]}, {"cell_type": "markdown", "id": "2856cef8", "metadata": {}, "source": ["# PSW Checking starts"]}, {"cell_type": "code", "execution_count": 123, "id": "8a67cf1d", "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "\n", "def parse_date(date_str):\n", "    \"\"\"Try parsing known date formats\"\"\"\n", "    for fmt in (\"%d-%b-%y\", \"%d-%m-%y\"):\n", "        try:\n", "            return datetime.strptime(date_str.strip(), fmt)\n", "        except ValueError:\n", "            \n", "            continue\n", "    raise ValueError(f\"Unrecognized date format: {date_str}\")"]}, {"cell_type": "code", "execution_count": 124, "id": "9dd7bb84", "metadata": {}, "outputs": [{"data": {"text/plain": ["'P80009246A'"]}, "execution_count": 124, "metadata": {}, "output_type": "execute_result"}], "source": ["PSW['customer_part_number']"]}, {"cell_type": "code", "execution_count": 125, "id": "6649415e", "metadata": {}, "outputs": [], "source": ["def get_key_ignore_case(d, target_key):\n", "    \"\"\"\n", "    Returns the value for a key in the dictionary `d` that matches `target_key` case-insensitively.\n", "    Returns None if the key doesn't exist.\n", "    \"\"\"\n", "    for k, v in d.items():\n", "        if k.lower() == target_key.lower():\n", "            return v\n", "    return None"]}, {"cell_type": "code", "execution_count": 127, "id": "c99ddc22", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'table_type': 'TYPE A', 'data': [{'S. No.': '1', 'DRG. No.': '...', 'Qty': '1', 'Description': 'PLATE BASE LOWER LINK', 'Size/Specification': 'WT: 3.480 Kg.', 'Matl.': 'SAE 5150 / En19 BS-970', 'RMKS': 'W/D'}, {'S. No.': '2', 'DRG. No.': 'P80009068A', 'Qty': '1', 'Description': 'BALL END ASSY', 'Size/Specification': None, 'Matl.': None, 'RMKS': None}, {'S. No.': '3', 'DRG. No.': 'P80009248A', 'Qty': '1', 'Description': 'BALL END ASSY', 'Size/Specification': None, 'Matl.': None, 'RMKS': None}]}\n", "{'table_type': 'TYPE B', 'data': {'DATE': '29-05-24', 'PART': 'LOWER LINK', 'DRAWING NO.': 'P80009246A', 'REV': '001', 'WEIGHT': '5.02', 'MATERIAL': 'AS PER BOM', 'Engineering Change level': '001'}}\n", "{'table_type': 'TYPE A', 'data': [{'S. No.': '1', 'DRG. No.': 'P280801', 'Qty': '1', 'Description': 'BALL CAT-1 (LOWER LINK)', 'Size/Specification': '', 'Matl.': '', 'RMKS': ''}, {'S. No.': '2', 'DRG. No.': 'P80009101A', 'Qty': '1', 'Description': 'BALL END', 'Size/Specification': '', 'Matl.': '', 'RMKS': ''}]}\n", "{'table_type': 'TYPE B', 'data': {'DATE': '26.11.96', 'PART': 'BALL END ASSY', 'DRAWING NO.': 'P80009068A', 'REV': '001', 'WEIGHT': '0.918', 'MATERIAL': 'AS PER BOM', 'Engineering Change level': '001'}}\n", "{'table_type': 'TYPE B', 'data': {'DATE': '10.8.21', 'PART': 'BALL CAT-1 (LOWER LINK)', 'DRAWING NO.': 'P280801', 'REV': 'A', 'WEIGHT': '~0.23', 'MATERIAL': 'SAE 8620 / 20MnCr1 / 20MnCr5 IS: 4432-67'}}\n", "{'table_type': 'TYPE B', 'data': {'DATE': '28-05-24', 'PART': 'BALL END ASSY', 'DRAWING NO.': 'P80009248A', 'REV': '001', 'WEIGHT': '0.962', 'MATERIAL': 'AS PER BOM', 'Engineering Change level': '001'}}\n", "{'table_type': 'TYPE B', 'data': {'DATE': '28-05-24', 'PART': 'BALL END', 'DRAWING NO.': 'P80009247A', 'REV': '001', 'WEIGHT': '0.738', 'MATERIAL': '27M02 IS:11169-1 (27C15)', 'Engineering Change level': '001'}}\n"]}], "source": ["obj=[]\n", "for i in images_json:\n", "    print(i)\n", "    if i['table_type']=='TYPE A':\n", "        for j in i['data']:\n", "            if j['DRG. No.']==PSW['customer_part_number']:\n", "                checks['customer_part_number']={'psw':PSW['customer_part_number'],'image':j['DRG. No.'], \n", "                                       'condition':True}\n", "                obj.append(j)\n", "\n", "    if i['table_type']=='TYPE B':\n", "        if PSW['customer_part_number']==i['data']['DRAWING NO.']:\n", "            checks['customer_part_number']={'psw':PSW['customer_part_number'],'image':i['data']['DRAWING NO.'], \n", "                                       'condition':True}\n", "            obj.append(i['data'])\n", "\n", "\n", "for i in obj:\n", "    if i['DRAWING NO.'].lower()==PSW['org_part_number'].lower():\n", "        checks['part_number']={'psw':PSW['org_part_number'],'image':i['DRAWING NO.'], 'condition':True}\n", "    else:\n", "        checks['part_number']={'psw':PSW['org_part_number'],'image':i['DRAWING NO.'], 'condition':False}\n", "\n", "    if i['PART'].lower() in PSW['part_name'].lower():\n", "        # print(i['PART'].lower(),PSW['part_name'].lower())\n", "        checks['Part_name']={'psw':PSW['part_name'],'image':i['PART'], 'condition':True}\n", "    else:\n", "        print(i['PART'].lower(),PSW['part_name'].lower())\n", "        checks['Part_name']={'psw':PSW['part_name'],'image':i['PART'], 'condition':False}\n", "    if (i['REV']=='001') and (PSW['engineering_change_level']=='A'):\n", "        checks['engineering_change_level']={'psw':PSW['engineering_change_level'],'image':i['REV'], 'condition':True}\n", "    else:\n", "        checks['engineering_change_level']={'psw':PSW['engineering_change_level'],'image':i['REV'], 'condition':False}\n", "    if i['WEIGHT']==PSW['weight']:\n", "        checks['weight']={'psw':PSW['weight'],'image':i['WEIGHT'], 'condition':True}\n", "    else:\n", "        checks['weight']={'psw':PSW['weight'],'image':i['WEIGHT'], 'condition':False}\n", "    try:\n", "        i_date = parse_date(i.get('DATE', ''))\n", "        psw_date = parse_date(PSW.get('date', ''))\n", "        if i_date.date() == psw_date.date():\n", "            checks['date'] = {'psw':PSW['date'],'image':i['DATE'], 'condition':True}\n", "        else:\n", "            checks['date'] = {'psw':PSW['date'],'image':i['DATE'], 'condition':False}\n", "    except ValueError as e:\n", "        print(\"Date format error:\", e)\n", "\n", "if PSW['purchase_order_no'] != None:\n", "    checks['purchase_order_no']={'psw':PSW['purchase_order_no'],'condition':True}\n", "else:\n", "    checks['purchase_order_no']={'psw':PSW['purchase_order_no'], 'condition':False}\n", "\n", "if PSW['customer_submission_info'] in (['MAHINDRA & MAHINDRA LTD.','SWARAJ', 'M&M']):\n", "    checks['customer_submission_info']={'psw':PSW['customer_submission_info'],'condition':True}\n", "else:\n", "    checks['customer_submission_info']={'psw':PSW['customer_submission_info'],'condition':False}\n", "\n", "if PSW['po_buyer_name']=='MR. VARUN KUMAR':\n", "    checks['po_buyer_name']={'psw':PSW['po_buyer_name'],'condition':True}\n", "else:\n", "    checks['po_buyer_name']={'psw':PSW['po_buyer_name'],'condition':False}\n", "\n", "if PSW['reason_for_submission'] != None:\n", "    checks['reason_for_submission']={'psw':PSW['reason_for_submission'],'condition':True}\n", "else:\n", "    checks['reason_for_submission']={'psw':PSW['reason_for_submission'],'condition':False}\n", "\n", "if 'level 3' in PSW['level_of_submission'].lower():\n", "    checks['level_of_submission']={'psw':PSW['level_of_submission'],'condition':True}\n", "else:\n", "    checks['level_of_submission']={'psw':PSW['level_of_submission'],'condition':False}\n", "\n", "if PSW['psw_signed_by_supplier']==True:\n", "    checks['psw_signed_by_supplier']={'psw':PSW['psw_signed_by_supplier'],'condition':True}\n", "else:\n", "    checks['psw_signed_by_supplier']={'psw':PSW['psw_signed_by_supplier'],'condition':False}\n", "\n", "if PSW['production_rate']!=None:\n", "    checks['production_rate']={'psw':PSW['production_rate'],'condition':True}\n", "else:\n", "    checks['production_rate']={'psw':PSW['production_rate'],'condition':False}"]}, {"cell_type": "markdown", "id": "361a0fed", "metadata": {}, "source": ["# Process Flow Diagram Checking"]}, {"cell_type": "code", "execution_count": 128, "id": "22c8bfdf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PLATE BASE LOWER LINK\n", "BALL END ASSY\n", "BALL END ASSY\n", "BALL CAT-1 (LOWER LINK)\n", "BALL END\n"]}], "source": ["if pfd_result['Supplier Information']['Part Number']==PSW['customer_part_number']:\n", "    checks['pfd_part_number']={'psw':PSW['customer_part_number'],'pfd':pfd_result['Supplier Information']['Part Number'], 'condition':True}\n", "else:\n", "    checks['pfd_part_number']={'psw':PSW['customer_part_number'],'pfd':pfd_result['Supplier Information']['Part Number'], 'condition':False}\n", "    \n", "if pfd_result['Supplier Information']['Part Name']==PSW['part_name']:\n", "    checks['pfd_part_name']={'psw':PSW['part_name'],'pfd':pfd_result['Supplier Information']['Part Name'], 'condition':True}\n", "else:\n", "    checks['pfd_part_name']={'psw':PSW['part_name'],'pfd':pfd_result['Supplier Information']['Part Name'], 'condition':False}\n", "\n", "if pfd_result['Supplier Information']['Supplier Name']==PSW['organization_manufacturing_info']['vendor_name']:\n", "    checks['pfd_supplier_name']={'psw':PSW['organization_manufacturing_info']['vendor_name'],'pfd':pfd_result['Supplier Information']['Supplier Name'], 'condition':True}\n", "else:\n", "    checks['pfd_supplier_name']={'psw':PSW['organization_manufacturing_info']['vendor_name'],'pfd':pfd_result['Supplier Information']['Supplier Name'], 'condition':False}\n", "\n", "if pfd_result['Supplier Information']['Supplier Code']==PSW['organization_manufacturing_info']['vendor_code']:\n", "    checks['pfd_supplier_code']={'psw':PSW['organization_manufacturing_info']['vendor_code'],'pfd':pfd_result['Supplier Information']['Supplier Code'], 'condition':True}\n", "else:\n", "    checks['pfd_supplier_code']={'psw':PSW['organization_manufacturing_info']['vendor_code'],'pfd':pfd_result['Supplier Information']['Supplier Code'], 'condition':False}\n", "\n", "if PSW['engineering_change_level'] in pfd_result['Supplier Information']['Drg. Latest Alteration']:\n", "    checks['pfd_engineering_change_level']={'psw':PSW['engineering_change_level'], 'condition':True}\n", "else:\n", "    checks['pfd_engineering_change_level']={'psw':PSW['engineering_change_level'], 'condition':False}\n", "\n", "\n", "for i in images_json:\n", "    if i['table_type']=='TYPE A':\n", "        for j in i['data']:\n", "            print(j['Description'])\n", "            if j['Description'] in pfd_result['Process Flow Details']:\n", "                checks[f\"pfd_{j['Description']}\"]={'image':j['Description'],'condition':True}\n", "            else:\n", "                checks[f\"pfd_{j['Description']}\"]={'image':j['Description'],'condition':False}\n"]}, {"cell_type": "markdown", "id": "308dca5b", "metadata": {}, "source": ["# PFMEA Matching"]}, {"cell_type": "code", "execution_count": 129, "id": "ba5b0a07", "metadata": {}, "outputs": [], "source": ["if pfmea_validation_criteria['part_info']['part_number']==PSW['customer_part_number']:\n", "    checks['pfmea_part_number']={'psw':PSW['customer_part_number'],'pfmea':pfmea_validation_criteria['part_info']['part_number'], 'condition':True}\n", "else:\n", "    checks['pfmea_part_number']={'psw':PSW['customer_part_number'],'pfmea':pfmea_validation_criteria['part_info']['part_number'], 'condition':False}\n", "\n", "if pfmea_validation_criteria['part_info']['item_name']==PSW['part_name']:\n", "    checks['pfmea_part_name']={'psw':PSW['part_name'],'pfmea':pfmea_validation_criteria['part_info']['item_name'], 'condition':True}\n", "else:\n", "    checks['pfmea_part_name']={'psw':PSW['part_name'],'pfmea':pfmea_validation_criteria['part_info']['item_name'], 'condition':False}\n", "\n", "for i in pfmea_validation_criteria['bom_child_list']:\n", "    # print(j['Description'])\n", "    if i in pfd_result['Process Flow Details']:\n", "        checks[f\"pfmea__{i}\"]={'pfmea':i,'condition':True}\n", "    else:\n", "        checks[f\"pfmea__{i}\"]={'pfmea':i,'condition':False}\n", "\n", "for i in images_json:\n", "    if i['table_type']=='TYPE A':\n", "        for j in i['data']:\n", "            if j['Description'] in pfmea_validation_criteria['bom_child_list']:\n", "                checks[f\"pfmea_bom_{j['Description']}\"]={'image':j['Description'],'condition':True}\n", "            else:\n", "                checks[f\"pfmea_bom_{j['Description']}\"]={'image':j['Description'],'condition':False}\n", "\n", "\n", "\n", "import re\n", "\n", "def normalize(text):\n", "    # Normalize by removing dashes, newlines, extra spaces and lowering case\n", "    text = text.lower()\n", "    text = re.sub(r'\\band\\b|&', ',', text)  # Replace 'and' or '&' with comma\n", "    text = re.sub(r'[\\s,]+', ' ', text)     # Normalize whitespace and commas\n", "    text = text.replace('tacking', 'tack')  # Simplify common word variants\n", "    text = text.replace('welding', 'weld')\n", "    return text.strip()\n", "\n", "for i in pfd_result['Process Flow Details']:\n", "    for j in pfmea_validation_criteria['dataframe']:\n", "        if i == j:\n", "            # Get unique PFMEA function list and normalize them\n", "            pfmea_df = pfmea_validation_criteria['dataframe'][j]\n", "            pfmea_func_unique = pfmea_df['PROCESS FUNCTIONAL REQUIRMENT'].unique()\n", "            pfmea_func_normalized_map = {\n", "                normalize(x): x for x in pfmea_func_unique\n", "            }\n", "            # print(pfmea_func_normalized_map)\n", "            for idx, k in enumerate(pfd_result['Process Flow Details'][i]['Processes']):\n", "                norm_k = normalize(k)\n", "\n", "                # print(\"norm_k\",norm_k,k)\n", "                if norm_k in pfmea_func_normalized_map:\n", "                    matched_func = pfmea_func_normalized_map[norm_k]\n", "                    # print(matched_func)\n", "                    # Get S.NO from PFMEA df where functional requirement matches\n", "                    pfmea_row = pfmea_df[['S.NO', 'PROCESS FUNCTIONAL REQUIRMENT']].drop_duplicates()\n", "                    pfmea_sno = pfmea_row[pfmea_row['PROCESS FUNCTIONAL REQUIRMENT'] == matched_func]['S.NO'].values[0]\n", "                    # print(pfmea_sno)\n", "                    if int(pfd_result['Process Flow Details'][i]['Processes'][k]) == int(pfmea_sno):\n", "                        checks[f'pfmea_{i}_{k}']={'pfd':pfd_result['Process Flow Details'][i]['Processes'][k],'pfmea':pfmea_sno, 'condition':True}\n", "                    else:\n", "                        # print(f\"⚠ Step mismatch for: {k} | PFD Step: {pfd_result['Process Flow Details'][i]['Processes'][k]}, PFMEA S.NO: {pfmea_sno}\")\n", "                        checks[f'pfmea_{i}_{k}']={'pfd':pfd_result['Process Flow Details'][i]['Processes'][k],'pfmea':pfmea_sno, 'condition':False}\n", "                else:\n", "                    checks[f'pfmea_{i}_{k}']={'pfd':pfd_result['Process Flow Details'][i]['Processes'][k], 'condition':False}\n", "\n", "flag = 0\n", "for i in pfmea_validation_criteria['dataframe']:\n", "    if pfmea_validation_criteria['dataframe'][i][pfmea_validation_criteria['dataframe'][i]['POTENTIAL EFFECT MODE'].isna()].size>0:\n", "        checks[f'pfmea_POTENTIAL EFFECT MODE__missing_data']={'condition':True}\n", "        flag = 1\n", "        break\n", "if flag == 0:\n", "    checks['pfmea_POTENTIAL EFFECT MODE_missing_data']={'condition':False}\n", "\n", "\n", "flag = 0\n", "for i in pfmea_validation_criteria['dataframe']:\n", "    if pfmea_validation_criteria['dataframe'][i][pfmea_validation_criteria['dataframe'][i]['POTENTIAL EFFECT MODE'].isna()].size>0:\n", "        checks[f'pfmea_POTENTIAL EFFECT MODE_missing_data']={'condition':True}\n", "        flag = 1\n", "        break\n", "if flag == 0:\n", "    checks['pfmea_POTENTIAL EFFECT MODE_missing_data']={'condition':False}\n", "\n", "flag = 0\n", "for i in pfmea_validation_criteria['dataframe']:\n", "    if pfmea_validation_criteria['dataframe'][i][pfmea_validation_criteria['dataframe'][i]['S E V'].isna()].size>0:\n", "        checks[f'pfmea_S E V_missing_data']={'condition':True}\n", "        flag = 1\n", "        break\n", "if flag == 0:\n", "    checks['pfmea_S E V_missing_data']={'condition':False}\n", "\n", "flag = 0\n", "for i in pfmea_validation_criteria['dataframe']:\n", "    df = pfmea_validation_criteria['dataframe'][i]\n", "    sev_filtered = df[df['S E V'] > 8]\n", "    if sev_filtered.empty:\n", "        checks[f'pfmea_S E V_POKA and FP_missing']={'condition':False}\n", "        flag = 1\n", "        break\n", "    # Check if either column contains \"POKA YOKO\" or \"Fool Proofing\"\n", "    has_poka_or_fp = (\n", "        sev_filtered['CURRENT PROCESS CONTROLS PREVENTION'].astype(str).str.contains(\"POKA YOKO\", case=False, na=False) |\n", "        sev_filtered['CURRENT PROCESS CONTROLS DETECTION'].astype(str).str.contains(\"POKA YOKO\", case=False, na=False) |\n", "        sev_filtered['CURRENT PROCESS CONTROLS PREVENTION'].astype(str).str.contains(\"Fool Proofing\", case=False, na=False) |\n", "        sev_filtered['CURRENT PROCESS CONTROLS DETECTION'].astype(str).str.contains(\"Fool Proofing\", case=False, na=False)\n", "    )\n", "    if ~has_poka_or_fp.any():\n", "        checks[f'pfmea_S E V_POKA and FP_missing']={'condition':True}\n", "        flag = 1\n", "        break\n", "    else:\n", "        checks[f'pfmea_S E V_POKA and FP_missing']={'condition':False}\n", "\n", "flag = 0\n", "for i in pfmea_validation_criteria['dataframe']:\n", "    if pfmea_validation_criteria['dataframe'][i][pfmea_validation_criteria['dataframe'][i]['CURRENT PROCESS CONTROLS PREVENTION'].isna()].size>0:\n", "        checks[f'pfmea_CURRENT PROCESS CONTROLS PREVENTION_missing_data']={'condition':True}\n", "        flag = 1\n", "        break\n", "if flag == 0:\n", "    checks['pfmea_CURRENT PROCESS CONTROLS PREVENTION_missing_data']={'condition':False}\n", "\n", "flag = 0\n", "for i in pfmea_validation_criteria['dataframe']:\n", "    flat_df = pfmea_validation_criteria['dataframe'][i].copy()\n", "\n", "    # Clean column names (optional but safer)\n", "    flat_df.columns = ['S.NO', 'PROCESS FUNCTIONAL REQUIRMENT', 'POTENTIAL FAILURE MODE', 'POTENTIAL EFFECT MODE',\n", "                       'S E V', 'POTENTI<PERSON> CAUSES', 'CURRENT PROCESS CONTROLS PREVENTION', 'OCCUR',\n", "                       'CURRENT PROCESS CONTROLS DETECTION', 'DETEC', 'R     P    N', 'RECOMMENDED ACTION',\n", "                       'RESPONSIBILITY & TARGET DATE', 'ACTION TAKEN', 'SEV_', 'OCCUR_', 'DETEC_', 'RPN_']\n", "    if flat_df['OCCUR_'].isna().any():\n", "        checks[f'pfmea_OCCUR_missing_data']={'condition':True}\n", "        print(i)\n", "        flag = 1\n", "        break\n", "if flag == 0:\n", "    checks['pfmea_OCCUR_missing_data']={'condition':False}\n", "\n", "flag = 0\n", "for i in pfmea_validation_criteria['dataframe']:\n", "    if pfmea_validation_criteria['dataframe'][i][pfmea_validation_criteria['dataframe'][i]['CURRENT PROCESS CONTROLS DETECTION'].isna()].size>0:\n", "        checks[f'pfmea_CURRENT PROCESS CONTROLS DETECTION_missing_data']={'condition':True}\n", "        flag = 1\n", "        break\n", "if flag == 0:\n", "    checks['pfmea_CURRENT PROCESS CONTROLS DETECTION_missing_data']={'condition':False}\n", "\n", "flag = 0\n", "for i in pfmea_validation_criteria['dataframe']:\n", "    flat_df = pfmea_validation_criteria['dataframe'][i].copy()\n", "\n", "    # Clean column names (optional but safer)\n", "    flat_df.columns = ['S.NO', 'PROCESS FUNCTIONAL REQUIRMENT', 'POTENTIAL FAILURE MODE', 'POTENTIAL EFFECT MODE',\n", "                       'S E V', 'POTENTI<PERSON> CAUSES', 'CURRENT PROCESS CONTROLS PREVENTION', 'OCCUR',\n", "                       'CURRENT PROCESS CONTROLS DETECTION', 'DETEC', 'R     P    N', 'RECOMMENDED ACTION',\n", "                       'RESPONSIBILITY & TARGET DATE', 'ACTION TAKEN', 'SEV_', 'OCCUR_', 'DETEC_', 'RPN_']\n", "    if flat_df['DETEC_'].isna().any():\n", "        checks[f'pfmea_DETEC_missing_data']={'condition':True}\n", "        print(i)\n", "        flag = 1\n", "        break\n", "if flag == 0:\n", "    checks['pfmea_DETEC_missing_data']={'condition':False}\n", "\n", "flag = 0\n", "\n", "for i in pfmea_validation_criteria['dataframe']:\n", "    flat_df = pfmea_validation_criteria['dataframe'][i].copy()\n", "\n", "    # Clean column names (optional but safer)\n", "    flat_df.columns = ['S.NO', 'PROCESS FUNCTIONAL REQUIRMENT', 'POTENTIAL FAILURE MODE', 'POTENTIAL EFFECT MODE',\n", "                       'S E V', 'POTENTI<PERSON> CAUSES', 'CURRENT PROCESS CONTROLS PREVENTION', 'OCCUR',\n", "                       'CURRENT PROCESS CONTROLS DETECTION', 'DETEC', 'R     P    N', 'RECOMMENDED ACTION',\n", "                       'RESPONSIBILITY & TARGET DATE', 'ACTION TAKEN', 'SEV_', 'OCCUR_', 'DETEC_', 'RPN_']\n", "\n", "    # Convert necessary columns to numeric\n", "    for col in ['SEV_', 'OCCUR_', 'DETEC_', 'RPN_']:\n", "        flat_df[col] = pd.to_numeric(flat_df[col], errors='coerce')\n", "\n", "    # Recalculate RPN\n", "    calculated_rpn = flat_df['SEV_'] * flat_df['OCCUR_'] * flat_df['DETEC_']\n", "\n", "    # Check for mismatches (use parentheses with ~)\n", "    mismatched = (calculated_rpn != flat_df['RPN_']) & (~flat_df['RPN_'].isna())\n", "\n", "    if mismatched.any():\n", "        checks[f'{i}_pfmea_RPN_Multiplication_data_failed'] = {'condition':True}\n", "        print(f\"❌ RPN mismatch in: {i}\")\n", "        flag = 1\n", "        break\n", "    else:\n", "        checks[f'{i}_pfmea_RPN_Multiplication_data_failed'] = {'condition':False}\n", "\n", "if flag == 0:\n", "    checks['pfmea_RPN_Multiplication_data_failed'] = {'condition':False}\n", "\n", "flag = 0\n", "\n", "for i in pfmea_validation_criteria['dataframe']:\n", "    flat_df = pfmea_validation_criteria['dataframe'][i].copy()\n", "\n", "    # Clean column names (optional but safer)\n", "    flat_df.columns = ['S.NO', 'PROCESS FUNCTIONAL REQUIRMENT', 'POTENTIAL FAILURE MODE', 'POTENTIAL EFFECT MODE',\n", "                       'S E V', 'POTENTI<PERSON> CAUSES', 'CURRENT PROCESS CONTROLS PREVENTION', 'OCCUR',\n", "                       'CURRENT PROCESS CONTROLS DETECTION', 'DETEC', 'R     P    N', 'RECOMMENDED ACTION',\n", "                       'RESPONSIBILITY & TARGET DATE', 'ACTION TAKEN', 'SEV_', 'OCCUR_', 'DETEC_', 'RPN_']\n", "    for col in ['SEV_', 'OCCUR_', 'DETEC_', 'RPN_']:\n", "        flat_df[col] = pd.to_numeric(flat_df[col], errors='coerce')\n", "    if flat_df['RECOMMENDED ACTION'][(flat_df['RPN_'])>100].isna().any():\n", "        checks[f'{i}_pfmea_RECOMMENDED ACTION_>100_missing_data'] = {'condition':True}\n", "        flag = 1\n", "        # break\n", "    else:\n", "        checks[f'{i}_pfmea_RECOMMENDED ACTION_.>100_missing_data'] = {'condition':False}\n", "\n", "flag = 0\n", "\n", "for i in pfmea_validation_criteria['dataframe']:\n", "    flat_df = pfmea_validation_criteria['dataframe'][i].copy()\n", "\n", "    # Clean column names (optional but safer)\n", "    flat_df.columns = ['S.NO', 'PROCESS FUNCTIONAL REQUIRMENT', 'POTENTIAL FAILURE MODE', 'POTENTIAL EFFECT MODE',\n", "                       'S E V', 'POTENTI<PERSON> CAUSES', 'CURRENT PROCESS CONTROLS PREVENTION', 'OCCUR',\n", "                       'CURRENT PROCESS CONTROLS DETECTION', 'DETEC', 'R     P    N', 'RECOMMENDED ACTION',\n", "                       'RESPONSIBILITY & TARGET DATE', 'ACTION TAKEN', 'SEV_', 'OCCUR_', 'DETEC_', 'RPN_']\n", "    for col in ['SEV_', 'R     P    N', 'DETEC_', 'RPN_']:\n", "        flat_df[col] = pd.to_numeric(flat_df[col], errors='coerce')\n", "    if flat_df[flat_df['RPN_']>100].shape[0]>0 or flat_df[flat_df['R     P    N']>100].shape[0]>0:\n", "        checks[f'{i}_pfmea_RPN>100_'] = {'condition':True}\n", "        flag = 1\n", "        # break\n", "    else:\n", "        checks[f'{i}_pfmea_RPN>100_'] = {'condition':False}\n", "\n", "flag = 0\n", "\n", "for i in pfmea_validation_criteria['dataframe']:\n", "    flat_df = pfmea_validation_criteria['dataframe'][i].copy()\n", "\n", "    # Clean column names (optional but safer)\n", "    flat_df.columns = ['S.NO', 'PROCESS FUNCTIONAL REQUIRMENT', 'POTENTIAL FAILURE MODE', 'POTENTIAL EFFECT MODE',\n", "                       'S E V', 'POTENTI<PERSON> CAUSES', 'CURRENT PROCESS CONTROLS PREVENTION', 'OCCUR',\n", "                       'CURRENT PROCESS CONTROLS DETECTION', 'DETEC', 'R     P    N', 'RECOMMENDED ACTION',\n", "                       'RESPONSIBILITY & TARGET DATE', 'ACTION TAKEN', 'SEV_', 'OCCUR_', 'DETEC_', 'RPN_']\n", "    if flat_df['RESPONSIBILITY & TARGET DATE'].isna().any():\n", "        checks[f'{i}_pfmea_RESPONSIBILITY & TARGET DATE_missing_data'] = {'condition':True}\n", "        flag = 1\n", "        # break\n", "    else:\n", "        checks[f'{i}_pfmea_RESPONSIBILITY & TARGET DATE_missing_data'] = {'condition':False}\n", "\n", "flag = 0\n", "\n", "for i in pfmea_validation_criteria['dataframe']:\n", "    flat_df = pfmea_validation_criteria['dataframe'][i].copy()\n", "\n", "    # Clean column names (optional but safer)\n", "    flat_df.columns = ['S.NO', 'PROCESS FUNCTIONAL REQUIRMENT', 'POTENTIAL FAILURE MODE', 'POTENTIAL EFFECT MODE',\n", "                       'S E V', 'POTENTI<PERSON> CAUSES', 'CURRENT PROCESS CONTROLS PREVENTION', 'OCCUR',\n", "                       'CURRENT PROCESS CONTROLS DETECTION', 'DETEC', 'R     P    N', 'RECOMMENDED ACTION',\n", "                       'RESPONSIBILITY & TARGET DATE', 'ACTION TAKEN', 'SEV_', 'OCCUR_', 'DETEC_', 'RPN_']\n", "    if flat_df[['OCCUR_', 'DETEC_','SEV_']][~flat_df['RESPONSIBILITY & TARGET DATE'].isna()].isna().any().any():\n", "        checks[f'{i}_pfmea_Ocur_Detec_Sev_data_missing'] = {'condition':True}\n", "        flag = 1\n", "        # break\n", "    else:\n", "        checks[f'{i}_pfmea_Ocur_Detec_Sev_data_missing'] = {'condition':False}\n", "\n"]}, {"cell_type": "markdown", "id": "0fd67690", "metadata": {}, "source": ["# Control Plan Matching"]}, {"cell_type": "code", "execution_count": 130, "id": "def73ce8", "metadata": {}, "outputs": [], "source": ["if control_data['part_info']['part_number']==pfd_result['Supplier Information']['Part Number'] and control_data['part_info']['part_number']==pfmea_validation_criteria['part_info']['part_number']:\n", "    checks['control_part_number']={'control_data':control_data['part_info']['part_number'],'pfd':pfd_result['Supplier Information']['Part Number'],'pfmea':pfmea_validation_criteria['part_info']['part_number'], 'condition':True}\n", "else:\n", "    checks['control_part_number']={'control_data':control_data['part_info']['part_number'],'pfd':pfd_result['Supplier Information']['Part Number'],'pfmea':pfmea_validation_criteria['part_info']['part_number'], 'condition':False}\n", "\n", "if control_data['part_info']['part_name']==pfd_result['Supplier Information']['Part Name'] and control_data['part_info']['part_name']==pfmea_validation_criteria['part_info']['item_name']:\n", "    checks['control_part_name']={'control_data':control_data['part_info']['part_name'],'pfd':pfd_result['Supplier Information']['Part Name'],'pfmea':pfmea_validation_criteria['part_info']['item_name'], 'condition':True}\n", "else:\n", "    checks['control_part_name']={'control_data':control_data['part_info']['part_name'],'pfd':pfd_result['Supplier Information']['Part Name'],'pfmea':pfmea_validation_criteria['part_info']['item_name'], 'condition':False}\n", "\n", "import re\n", "\n", "def normalize(text):\n", "    # Normalize by removing dashes, newlines, extra spaces and lowering case\n", "    text = text.lower()\n", "\n", "    # Replace 'and' or '&' with comma\n", "    text = re.sub(r'\\band\\b|&', ',', text)\n", "\n", "    # Replace hyphens with a space\n", "    text = text.replace('-', ' ')\n", "\n", "    # Simplify common word variants\n", "    text = text.replace('tacking', 'tack')\n", "    text = text.replace('welding', 'weld')\n", "\n", "    # Remove multiple spaces and commas and normalize to single space\n", "    text = re.sub(r'[\\s,]+', ' ', text)\n", "\n", "    # Final strip to remove leading/trailing spaces\n", "    return text.strip()\n", "\n", "for i in pfd_result['Process Flow Details']:\n", "    for j in control_data['dataframe']:\n", "        if i == j:\n", "            # Get unique PFMEA function list and normalize them\n", "            control_temp_df = control_data['dataframe'][j]\n", "            cp_func_unique = control_temp_df['Process Name / Operation Description.'].unique()\n", "            cp_func_normalized_map = {\n", "                normalize(x): x for x in cp_func_unique\n", "            }\n", "            # print(cp_func_normalized_map)\n", "            for idx, k in enumerate(pfd_result['Process Flow Details'][i]['Processes']):\n", "                norm_k = normalize(k)\n", "\n", "                # print(\"norm_k\",norm_k,k)\n", "                if norm_k in cp_func_normalized_map:\n", "                    matched_func = cp_func_normalized_map[norm_k]\n", "                    # print(matched_func)\n", "                    # Get Part / Process Number. from PFMEA df where functional requirement matches\n", "                    cp_row = control_temp_df[['Part / Process Number.', 'Process Name / Operation Description.']].drop_duplicates()\n", "                    cp_sno = cp_row[cp_row['Process Name / Operation Description.'] == matched_func]['Part / Process Number.'].values[0]\n", "                    # print(cp_sno)\n", "                    if int(pfd_result['Process Flow Details'][i]['Processes'][k]) == int(cp_sno):\n", "                        checks[f'cp_{i}_{k}_']={'pfd':pfd_result['Process Flow Details'][i]['Processes'][k],'cp':cp_sno, 'condition':True}\n", "                    else:\n", "                        # print(f\"⚠ Step mismatch for: {k} | PFD Step: {pfd_result['Process Flow Details'][i]['Processes'][k]}, PFMEA Part / Process Number.: {cp_sno}\")\n", "                        checks[f'cp_{i}_{k}_']={'pfd':pfd_result['Process Flow Details'][i]['Processes'][k],'cp':cp_sno, 'condition':False}\n", "                else:\n", "                    checks[f'cp_{i}_{k}']={'pfd':pfd_result['Process Flow Details'][i]['Processes'][k], 'condition':False}\n", "\n", "for i in control_data['dataframe']:\n", "    if control_data['dataframe'][i]['Process Name / Operation Description.'].isna().any():\n", "        checks[f'cp_{i}_Process Name / Operation Description._missing_data']={'condition':True}\n", "    else:\n", "        checks[f'cp_{i}_Process Name / Operation Description._missing_data']={'condition':False}\n", "\n", "for i in control_data['dataframe']:\n", "    if control_data['dataframe'][i]['Part / Process Number.'].isna().any():\n", "        checks[f'cp_{i}_Part / Process Number._missing_data']={'condition':True}\n", "    else:\n", "        checks[f'cp_{i}_Part / Process Number._missing_data']={'condition':False}\n", "\n", "for i in control_data['dataframe']:\n", "    if control_data['dataframe'][i]['Machine Device Jig Tool For Mfg.'].duplicated().any():\n", "        checks[f'cp_{i}_Machine Device Jig Tool For Mfg._duplicate_data']={'condition':True}\n", "    else:\n", "        checks[f'cp_{i}_Machine Device Jig Tool For Mfg._duplicate_data']={'condition':False}\n", "\n", "for i in control_data['dataframe']:\n", "    if control_data['dataframe'][i]['Product/Process Specification.'].isna().any():\n", "        checks[f'cp_{i}_Product/Process Specification._missing_data']={'condition':True}\n", "    else:\n", "        checks[f'cp_{i}_Product/Process Specification._missing_data']={'condition':False}\n", "\n", "for i in control_data['dataframe']:\n", "    if control_data['dataframe'][i]['Evaluation Measurement Technique.'].isna().any():\n", "        checks[f'cp_{i}_Evaluation Measurement Technique._missing_data']={'condition':True}\n", "    else:\n", "        checks[f'cp_{i}_Evaluation Measurement Technique._missing_data']={'condition':False}\n", "\n", "for i in control_data['dataframe']:\n", "    if control_data['dataframe'][i]['Reaction Plan & Corrective action'].isna().any():\n", "        checks[f'cp_{i}_Reaction Plan & Corrective action_missing_data']={'condition':True}\n", "    else:\n", "        checks[f'cp_{i}_Reaction Plan & Corrective action_missing_data']={'condition':False}\n", "\n", "for i in control_data['dataframe']:\n", "    if control_data['dataframe'][i]['Control Method.'].isna().any():\n", "        checks[f'cp_{i}_Control Method._missing_data']={'condition':True}\n", "    else:\n", "        checks[f'cp_{i}_Control Method._missing_data']={'condition':False}\n", "\n", "for i in control_data['dataframe']:\n", "    if control_data['dataframe'][i]['Frequency.'].isna().any():\n", "        checks[f'cp_{i}_Frequency._missing_data']={'condition':True}\n", "    else:\n", "        checks[f'cp_{i}_Frequency._missing_data']={'condition':False}\n", "\n", "if control_data['dataframe'][i].shape[1]!=13:\n", "    checks[f'cp_column_count_mismatch']={'condition':True}\n", "else:\n", "    checks[f'cp_column_count_mismatch']={'condition':False}\n"]}, {"cell_type": "code", "execution_count": 131, "id": "c118d39b", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'customer_part_number': {'psw': 'P80009246A',\n", "  'image': 'P80009246A',\n", "  'condition': True},\n", " 'part_number': {'psw': 'P80009246A',\n", "  'image': 'P80009246A',\n", "  'condition': True},\n", " 'Part_name': {'psw': 'LOWER LINK ASSY.',\n", "  'image': 'LOWER LINK',\n", "  'condition': True},\n", " 'engineering_change_level': {'psw': 'A', 'image': '001', 'condition': True},\n", " 'weight': {'psw': '5.02', 'image': '5.02', 'condition': True},\n", " 'date': {'psw': '29-May-24', 'image': '29-05-24', 'condition': True},\n", " 'purchase_order_no': {'psw': '6711009828', 'condition': True},\n", " 'customer_submission_info': {'psw': 'MAHINDRA & MAHINDRA LTD.',\n", "  'condition': True},\n", " 'po_buyer_name': {'psw': 'MR. VARUN KUMAR', 'condition': True},\n", " 'reason_for_submission': {'psw': 'Initial Submission', 'condition': True},\n", " 'level_of_submission': {'psw': 'Level 3 - Warrant with product samples and complete supporting data submitted to customer.',\n", "  'condition': True},\n", " 'psw_signed_by_supplier': {'psw': True, 'condition': True},\n", " 'production_rate': {'psw': '60 No.s 8 hours.', 'condition': True},\n", " 'pfd_part_number': {'psw': 'P80009246A',\n", "  'pfd': 'P80009246A',\n", "  'condition': True},\n", " 'pfd_part_name': {'psw': 'LOWER LINK ASSY.',\n", "  'pfd': 'LOWER LINK ASSY.',\n", "  'condition': True},\n", " 'pfd_supplier_name': {'psw': 'JAYCEE STRIPS AND FASTENERS PVT. LTD.',\n", "  'pfd': 'JAYCEE STRIPS AND FASTENERS PVT. LTD.',\n", "  'condition': True},\n", " 'pfd_supplier_code': {'psw': 'DDJ00202AA',\n", "  'pfd': 'DDJ00202AA',\n", "  'condition': True},\n", " 'pfd_engineering_change_level': {'psw': 'A', 'condition': True},\n", " 'pfd_PLATE BASE LOWER LINK': {'image': 'PLATE BASE LOWER LINK',\n", "  'condition': False},\n", " 'pfd_BALL END ASSY': {'image': 'BALL END ASSY', 'condition': False},\n", " 'pfd_BALL CAT-1 (LOWER LINK)': {'image': 'BALL CAT-1 (LOWER LINK)',\n", "  'condition': False},\n", " 'pfd_BALL END': {'image': 'BALL END', 'condition': False},\n", " 'pfmea_part_number': {'psw': 'P80009246A',\n", "  'pfmea': 'P80009246A',\n", "  'condition': True},\n", " 'pfmea_part_name': {'psw': 'LOWER LINK ASSY.',\n", "  'pfmea': 'LOWER LINK ASSY.',\n", "  'condition': True},\n", " 'pfmea__FLAT': {'pfmea': 'FLAT', 'condition': True},\n", " 'pfmea__BALL END-P80009101A': {'pfmea': 'BALL END-P80009101A',\n", "  'condition': False},\n", " 'pfmea__BALL END CAT - P80009247A': {'pfmea': 'BALL END CAT - P80009247A',\n", "  'condition': False},\n", " 'pfmea__BALL CAT 1 - P280801': {'pfmea': 'BALL CAT 1 - P280801',\n", "  'condition': False},\n", " 'pfmea__LOWER LINK (SUB ASSY.)': {'pfmea': 'LOWER LINK (SUB ASSY.)',\n", "  'condition': True},\n", " 'pfmea__LOWER LINK ASSY.': {'pfmea': 'LOWER LINK ASSY.', 'condition': True},\n", " 'pfmea_bom_PLATE BASE LOWER LINK': {'image': 'PLATE BASE LOWER LINK',\n", "  'condition': False},\n", " 'pfmea_bom_BALL END ASSY': {'image': 'BALL END ASSY', 'condition': False},\n", " 'pfmea_bom_BALL CAT-1 (LOWER LINK)': {'image': 'BALL CAT-1 (LOWER LINK)',\n", "  'condition': False},\n", " 'pfmea_bom_BALL END': {'image': 'BALL END', 'condition': False},\n", " 'pfmea_FLAT_CUTTING': {'pfd': '10', 'pfmea': 10, 'condition': True},\n", " 'pfmea_FLAT_1st SIDE CHAMFER GRINDING': {'pfd': '20',\n", "  'pfmea': 20,\n", "  'condition': True},\n", " 'pfmea_FLAT_2nd SIDE CHAMFER GRINDING': {'pfd': '30',\n", "  'pfmea': 30,\n", "  'condition': True},\n", " 'pfmea_FLAT_VMC DRILLING': {'pfd': '40', 'pfmea': 40, 'condition': True},\n", " 'pfmea_FLAT_CHAMFERING': {'pfd': '50', 'pfmea': 50, 'condition': True},\n", " 'pfmea_FLAT_PRE-HEATING & BENDING 1': {'pfd': '60', 'condition': False},\n", " 'pfmea_FLAT_PRE-HEATING & BENDING-2': {'pfd': '70', 'condition': False},\n", " 'pfmea_FLAT_HARDENING & TEMPERING': {'pfd': '80',\n", "  'pfmea': 80,\n", "  'condition': True},\n", " 'pfmea_LOWER LINK (SUB ASSY.)_PART a, b & c (TACKING & WELDING)': {'pfd': '180',\n", "  'pfmea': 180,\n", "  'condition': True},\n", " 'pfmea_LOWER LINK (SUB ASSY.)_WELD JOINT NORMALIZING': {'pfd': '190',\n", "  'condition': False},\n", " 'pfmea_LOWER LINK (SUB ASSY.)_SHOT BLASTING': {'pfd': '200',\n", "  'pfmea': 200,\n", "  'condition': True},\n", " 'pfmea_LOWER LINK (SUB ASSY.)_PRIMER COATING (CHARCOAL GREY COLOUR)': {'pfd': '210',\n", "  'condition': False},\n", " 'pfmea_LOWER LINK ASSY._PART d, e (BALL CRIMPING)': {'pfd': '220',\n", "  'pfmea': 220,\n", "  'condition': True},\n", " 'pfmea_LOWER LINK ASSY._TRACEBILITY CODE (PUNCHING)': {'pfd': '230',\n", "  'pfmea': 230,\n", "  'condition': True},\n", " 'pfmea_POTENTIAL EFFECT MODE_missing_data': {'condition': False},\n", " 'pfmea_S E V_missing_data': {'condition': True},\n", " 'pfmea_S E V_POKA and FP_missing': {'condition': False},\n", " 'pfmea_CURRENT PROCESS CONTROLS PREVENTION_missing_data': {'condition': False},\n", " 'pfmea_OCCUR_missing_data': {'condition': False},\n", " 'pfmea_CURRENT PROCESS CONTROLS DETECTION_missing_data': {'condition': False},\n", " 'pfmea_DETEC_missing_data': {'condition': False},\n", " 'FLAT_pfmea_RPN_Multiplication_data_failed': {'condition': False},\n", " 'BALL END-P80009101A_pfmea_RPN_Multiplication_data_failed': {'condition': False},\n", " 'BALL END CAT - P80009247A_pfmea_RPN_Multiplication_data_failed': {'condition': False},\n", " 'BALL CAT 1 - P280801_pfmea_RPN_Multiplication_data_failed': {'condition': False},\n", " 'LOWER LINK (SUB ASSY.)_pfmea_RPN_Multiplication_data_failed': {'condition': False},\n", " 'LOWER LINK ASSY._pfmea_RPN_Multiplication_data_failed': {'condition': False},\n", " 'pfmea_RPN_Multiplication_data_failed': {'condition': False},\n", " 'FLAT_pfmea_RECOMMENDED ACTION_.>100_missing_data': {'condition': False},\n", " 'BALL END-P80009101A_pfmea_RECOMMENDED ACTION_.>100_missing_data': {'condition': False},\n", " 'BALL END CAT - P80009247A_pfmea_RECOMMENDED ACTION_.>100_missing_data': {'condition': False},\n", " 'BALL CAT 1 - P280801_pfmea_RECOMMENDED ACTION_.>100_missing_data': {'condition': False},\n", " 'LOWER LINK (SUB ASSY.)_pfmea_RECOMMENDED ACTION_.>100_missing_data': {'condition': False},\n", " 'LOWER LINK ASSY._pfmea_RECOMMENDED ACTION_.>100_missing_data': {'condition': False},\n", " 'FLAT_pfmea_RPN>100_': {'condition': True},\n", " 'BALL END-P80009101A_pfmea_RPN>100_': {'condition': False},\n", " 'BALL END CAT - P80009247A_pfmea_RPN>100_': {'condition': False},\n", " 'BALL CAT 1 - P280801_pfmea_RPN>100_': {'condition': True},\n", " 'LOWER LINK (SUB ASSY.)_pfmea_RPN>100_': {'condition': True},\n", " 'LOWER LINK ASSY._pfmea_RPN>100_': {'condition': False},\n", " 'FLAT_pfmea_RESPONSIBILITY & TARGET DATE_missing_data': {'condition': False},\n", " 'BALL END-P80009101A_pfmea_RESPONSIBILITY & TARGET DATE_missing_data': {'condition': False},\n", " 'BALL END CAT - P80009247A_pfmea_RESPONSIBILITY & TARGET DATE_missing_data': {'condition': False},\n", " 'BALL CAT 1 - P280801_pfmea_RESPONSIBILITY & TARGET DATE_missing_data': {'condition': False},\n", " 'LOWER LINK (SUB ASSY.)_pfmea_RESPONSIBILITY & TARGET DATE_missing_data': {'condition': False},\n", " 'LOWER LINK ASSY._pfmea_RESPONSIBILITY & TARGET DATE_missing_data': {'condition': False},\n", " 'FLAT_pfmea_Ocur_Detec_Sev_data_missing': {'condition': False},\n", " 'BALL END-P80009101A_pfmea_Ocur_Detec_Sev_data_missing': {'condition': False},\n", " 'BALL END CAT - P80009247A_pfmea_Ocur_Detec_Sev_data_missing': {'condition': False},\n", " 'BALL CAT 1 - P280801_pfmea_Ocur_Detec_Sev_data_missing': {'condition': False},\n", " 'LOWER LINK (SUB ASSY.)_pfmea_Ocur_Detec_Sev_data_missing': {'condition': False},\n", " 'LOWER LINK ASSY._pfmea_Ocur_Detec_Sev_data_missing': {'condition': False},\n", " 'control_part_number': {'control_data': 'P80009246A',\n", "  'pfd': 'P80009246A',\n", "  'pfmea': 'P80009246A',\n", "  'condition': True},\n", " 'control_part_name': {'control_data': 'LOWER LINK ASSY.',\n", "  'pfd': 'LOWER LINK ASSY.',\n", "  'pfmea': 'LOWER LINK ASSY.',\n", "  'condition': True},\n", " 'cp_FLAT_CUTTING_': {'pfd': '10', 'cp': 10, 'condition': True},\n", " 'cp_FLAT_1st SIDE CHAMFER GRINDING_': {'pfd': '20',\n", "  'cp': 20,\n", "  'condition': True},\n", " 'cp_FLAT_2nd SIDE CHAMFER GRINDING_': {'pfd': '30',\n", "  'cp': 30,\n", "  'condition': True},\n", " 'cp_FLAT_VMC DRILLING_': {'pfd': '40', 'cp': 40, 'condition': True},\n", " 'cp_FLAT_CHAMFERING_': {'pfd': '50', 'cp': 50, 'condition': True},\n", " 'cp_FLAT_PRE-HEATING & BENDING 1_': {'pfd': '60',\n", "  'cp': 60,\n", "  'condition': True},\n", " 'cp_FLAT_PRE-HEATING & BENDING-2': {'pfd': '70', 'condition': False},\n", " 'cp_FLAT_HARDENING & TEMPERING_': {'pfd': '80', 'cp': 80, 'condition': True},\n", " 'cp_LOWER LINK (SUB ASSY.)_PART a, b & c (TACKING & WELDING)_': {'pfd': '180',\n", "  'cp': 180,\n", "  'condition': True},\n", " 'cp_LOWER LINK (SUB ASSY.)_WELD JOINT NORMALIZING': {'pfd': '190',\n", "  'condition': False},\n", " 'cp_LOWER LINK (SUB ASSY.)_SHOT BLASTING_': {'pfd': '200',\n", "  'cp': 200,\n", "  'condition': True},\n", " 'cp_LOWER LINK (SUB ASSY.)_PRIMER COATING (CHARCOAL GREY COLOUR)': {'pfd': '210',\n", "  'condition': False},\n", " 'cp_FLAT_Process Name / Operation Description._missing_data': {'condition': False},\n", " 'cp_BALL END-P80009101A_Process Name / Operation Description._missing_data': {'condition': False},\n", " 'cp_BALL END CAT - P80009247A_Process Name / Operation Description._missing_data': {'condition': False},\n", " 'cp_BALL CAT 1 - P280801_Process Name / Operation Description._missing_data': {'condition': False},\n", " 'cp_LOWER LINK (SUB ASSY.)_Process Name / Operation Description._missing_data': {'condition': False},\n", " 'cp_FLAT_Part / Process Number._missing_data': {'condition': False},\n", " 'cp_BALL END-P80009101A_Part / Process Number._missing_data': {'condition': False},\n", " 'cp_BALL END CAT - P80009247A_Part / Process Number._missing_data': {'condition': False},\n", " 'cp_BALL CAT 1 - P280801_Part / Process Number._missing_data': {'condition': False},\n", " 'cp_LOWER LINK (SUB ASSY.)_Part / Process Number._missing_data': {'condition': False},\n", " 'cp_FLAT_Machine Device Jig Tool For Mfg._duplicate_data': {'condition': True},\n", " 'cp_BALL END-P80009101A_Machine Device Jig <PERSON>l For Mfg._duplicate_data': {'condition': True},\n", " 'cp_BALL END CAT - P80009247A_Machine Device Jig <PERSON>l For Mfg._duplicate_data': {'condition': True},\n", " 'cp_BALL CAT 1 - P280801_Machine Device Jig <PERSON>l For Mfg._duplicate_data': {'condition': True},\n", " 'cp_LOWER LINK (SUB ASSY.)_Machine Device Jig <PERSON>l For Mfg._duplicate_data': {'condition': True},\n", " 'cp_FLAT_Product/Process Specification._missing_data': {'condition': False},\n", " 'cp_BALL END-P80009101A_Product/Process Specification._missing_data': {'condition': False},\n", " 'cp_BALL END CAT - P80009247A_Product/Process Specification._missing_data': {'condition': False},\n", " 'cp_BALL CAT 1 - P280801_Product/Process Specification._missing_data': {'condition': False},\n", " 'cp_LOWER LINK (SUB ASSY.)_Product/Process Specification._missing_data': {'condition': False},\n", " 'cp_FLAT_Evaluation Measurement Technique._missing_data': {'condition': False},\n", " 'cp_BALL END-P80009101A_Evaluation Measurement Technique._missing_data': {'condition': False},\n", " 'cp_BALL END CAT - P80009247A_Evaluation Measurement Technique._missing_data': {'condition': False},\n", " 'cp_BALL CAT 1 - P280801_Evaluation Measurement Technique._missing_data': {'condition': False},\n", " 'cp_LOWER LINK (SUB ASSY.)_Evaluation Measurement Technique._missing_data': {'condition': False},\n", " 'cp_FLAT_Reaction Plan & Corrective action_missing_data': {'condition': False},\n", " 'cp_BALL END-P80009101A_Reaction Plan & Corrective action_missing_data': {'condition': False},\n", " 'cp_BALL END CAT - P80009247A_Reaction Plan & Corrective action_missing_data': {'condition': False},\n", " 'cp_BALL CAT 1 - P280801_Reaction Plan & Corrective action_missing_data': {'condition': False},\n", " 'cp_LOWER LINK (SUB ASSY.)_Reaction Plan & Corrective action_missing_data': {'condition': False},\n", " 'cp_FLAT_Control Method._missing_data': {'condition': False},\n", " 'cp_BALL END-P80009101A_Control Method._missing_data': {'condition': False},\n", " 'cp_BALL END CAT - P80009247A_Control Method._missing_data': {'condition': False},\n", " 'cp_BALL CAT 1 - P280801_Control Method._missing_data': {'condition': False},\n", " 'cp_LOWER LINK (SUB ASSY.)_Control Method._missing_data': {'condition': True},\n", " 'cp_FLAT_Frequency._missing_data': {'condition': False},\n", " 'cp_BALL END-P80009101A_Frequency._missing_data': {'condition': False},\n", " 'cp_BALL END CAT - P80009247A_Frequency._missing_data': {'condition': False},\n", " 'cp_BALL CAT 1 - P280801_Frequency._missing_data': {'condition': False},\n", " 'cp_LOWER LINK (SUB ASSY.)_Frequency._missing_data': {'condition': True},\n", " 'cp_column_count_mismatch': {'condition': False}}"]}, "execution_count": 131, "metadata": {}, "output_type": "execute_result"}], "source": ["checks"]}, {"cell_type": "code", "execution_count": null, "id": "42200f22", "metadata": {}, "outputs": [], "source": ["import google.generativeai as genai\n", "from reportlab.lib.pagesizes import A4\n", "from reportlab.pdfgen import canvas\n", "from reportlab.lib.units import inch\n", "from textwrap import wrap\n", "\n", "# Step 1: Generate content using Gemini\n", "def generate_pdf_content(context, api_key):\n", "    genai.configure(api_key=api_key)\n", "    model = genai.GenerativeModel('gemini-1.5-flash')\n", "\n", "    prompt = f\"\"\"\n", "    Based on the following part manufacturing data, generate a detailed PDF-style summary with clear section headers and bullet points:\n", "\n", "    {context}\n", "\n", "    The output should be in Natural text or Markdown format.\n", "    \"\"\"\n", "\n", "    response = model.generate_content(prompt)\n", "    return response.text\n", "\n", "# Step 2: Save to PDF using ReportLab\n", "def write_pdf(text, output_file, font_name=\"Helvetica\", font_size=11, margin=1*inch):\n", "    c = canvas.Canvas(output_file, pagesize=A4)\n", "    width, height = A4\n", "\n", "    max_width = width - 2 * margin\n", "    y_position = height - margin\n", "\n", "    c.set<PERSON>ont(font_name, font_size)\n", "\n", "    for paragraph in text.split('\\n'):\n", "        wrapped_lines = wrap(paragraph, width=95)\n", "        for line in wrapped_lines:\n", "            if y_position < margin:\n", "                c.showPage()\n", "                c.set<PERSON>ont(font_name, font_size)\n", "                y_position = height - margin\n", "            c.drawString(margin, y_position, line)\n", "            y_position -= font_size + 2\n", "\n", "    c.save()\n", "    print(f\"✅ PDF saved to {output_file}\")\n", "\n", "\n", "\n", "\n", "content = generate_pdf_content(checks, GEMINI_API_KEY)\n", "write_pdf(content, \"output.pdf\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "2d6d9dc4", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "s<PERSON><PERSON><PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}