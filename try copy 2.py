GEMINI_API_KEY = "AIzaSyAqeGEgGgAwSuF-1UcimRSa64-973-5cB4"

import google.generativeai as genai
import PyPDF2
from PIL import Image
import pdf2image
import io
import os
import re
import json
import pandas as pd
import os
import json
import google.generativeai as genai
from concurrent.futures import ThreadPoolExecutor, as_completed

def extract_text_from_pdf_gemini(pdf_path, api_key):
    """
    Extract text from a PDF file using Google Gemini Flash 1.5
   
    Args:
        pdf_path (str): Path to the PDF file
        api_key (str): Your Google Gemini API key
       
    Returns:
        str: All extracted text from the PDF
    """
    try:
        # Configure Gemini
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel('gemini-2.5-flash')
       
        # First try PyPDF2 for text-based PDFs
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
               
                # If we got meaningful text, return it
                if text.strip() and len(text.strip()) > 50:
                    return text.strip()
        except:
            pass
       
        # If PyPDF2 failed or returned minimal text, use Gemini with images
        print("Converting PDF to images for OCR...")
       
        # Convert PDF pages to images
        images = pdf2image.convert_from_path(pdf_path)
       
        all_text = []
       
        for i, image in enumerate(images):
            print(f"Processing page {i+1}/{len(images)}")
           
            # Convert PIL image to bytes
            img_byte_arr = io.BytesIO()
            image.save(img_byte_arr, format='PNG')
            img_byte_arr = img_byte_arr.getvalue()
           
            # Upload image to Gemini
            uploaded_file = genai.upload_file(
                path=None,
                mime_type="image/png",
                data=img_byte_arr
            )
           
            # Extract text using Gemini
            response = model.generate_content([
                uploaded_file,
                "Extract all the text from this image. Return the text content and the tickboxes, no explanations or formatting."
            ])
           
            if response.text:
                all_text.append(response.text.strip())
           
            # Clean up uploaded file
            genai.delete_file(uploaded_file.name)
       
        return '\n\n'.join(all_text)
       
    except Exception as e:
        print(f"Error: {e}")
        return None
 
def extract_text_simple_gemini(pdf_path, api_key):
    """
    Simple text extraction using Gemini Flash 1.5 by uploading PDF directly
   
    Args:
        pdf_path (str): Path to the PDF file
        api_key (str): Your Google Gemini API key
       
    Returns:
        str: All extracted text from the PDF
    """
    try:
        # Configure Gemini
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel('gemini-2.5-flash')
       
        print(f"Uploading PDF: {pdf_path}")
       
        # Upload PDF directly to Gemini
        uploaded_file = genai.upload_file(
            path=pdf_path,
            mime_type="application/pdf"
        )
       
        print("Extracting text...")
       
        # Extract text using Gemini
        response = model.generate_content([
            uploaded_file,
            "Extract all the text from this PDF document. create a json of that for validation"
        ])
       
        # Clean up uploaded file
        genai.delete_file(uploaded_file.name)
       
        if response.text:
            return response.text.strip()
        else:
            return "No text extracted"
           
    except Exception as e:
        print(f"Error: {e}")
        return None

def extract_text_excel_gemini(excel_path, api_key):
    """
    Simple text extraction using Gemini Flash 1.5 by uploading Excel file directly
    
    Args:
        excel_path (str): Path to the Excel file (.xlsx, .xls)
        api_key (str): Your Google Gemini API key
        
    Returns:
        str: All extracted text from the Excel file
    """
    try:
        # Configure Gemini
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel('gemini-2.5-flash')
        
        print(f"Uploading Excel file: {excel_path}")
        
        # Determine MIME type based on file extension
        if excel_path.lower().endswith('.xlsx'):
            mime_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        elif excel_path.lower().endswith('.xls'):
            mime_type = "application/vnd.ms-excel"
        else:
            raise ValueError("Unsupported file format. Please use .xlsx or .xls files.")
        
        # Upload Excel file directly to Gemini
        uploaded_file = genai.upload_file(
            path=excel_path,
            mime_type=mime_type
        )
        
        print("Extracting text...")
        
        # Extract text using Gemini
        response = model.generate_content([
            uploaded_file,
            "Extract all the text content from this Excel file. Include data from all worksheets, column headers, and cell values. Create a JSON structure for validation that organizes the data by worksheet."
        ])
        
        # Clean up uploaded file
        genai.delete_file(uploaded_file.name)
        
        if response.text:
            return response.text.strip()
        else:
            return "No text extracted"
            
    except Exception as e:
        print(f"Error: {e}")
        return None

def extract_and_parse_json(text):
    try:
        # Extract substring starting from first {
        match = re.search(r'\{.*\}', text, re.DOTALL)
        if match:
            json_str = match.group()
            # print(json_str)
            return json.loads(json_str)
        else:
            print("No JSON object found.")
            return None
    except json.JSONDecodeError as e:
        print("Invalid JSON format:", e)
        return None 





def extract_text_from_image(model, file_path, filename):
    try:
        mime_type = "image/png" if filename.lower().endswith(".png") else "image/jpeg"
        uploaded_file = genai.upload_file(path=file_path, mime_type=mime_type)

        prompt = """
        Extract all the following data from images if available and Return in JSON
        IF IMAGE CONTAIN INFORMATION LIKE THESE:- DRG. No., Qty, Description, Size/Specification, Matl., RMKS
        IT'S TYPE 'A'
        RESPONSE IN THIS FORMAT 
        EXAMPLE:- {
            "table_type": "TYPE A",
            "data": [
                {
                "S. No.": ,
                "DRG. No.": ,
                "Qty": ,
                "Description": ,
                "Size/Specification": ,
                "Matl.": ,
                "RMKS": 
                }
            ]
        }

        IF IMAGE CONTAIN INFORMATION LIKE THESE:- DATE ,PART ,DRAWING NO. ,REV ,WEIGHT  ,MATERIAL
        IT'S TYPE 'B' 
        RESPONSE IN THIS FORMAT 
        EXAMPLE:- {
            "table_type": "TYPE B",
            "data": {
                "DATE": ,
                "PART": ,
                "DRAWING NO.": ,
                "REV": ,
                "WEIGHT": ,
                "MATERIAL": ,
                "Engineering Change level": "001"
            }            
        }
        """

        response = model.generate_content([uploaded_file, prompt])
        genai.delete_file(uploaded_file.name)

        if response.text:
            return filename, response.text.strip()
        else:
            return filename, "No text extracted"

    except Exception as e:
        return filename, f"Error: {e}"

def extract_text_from_images_in_folder_parallel(folder_path, api_key, output_json_path=None, max_workers=5):
    genai.configure(api_key=api_key)
    model = genai.GenerativeModel('gemini-2.5-flash')

    supported_extensions = ('.png', '.jpg', '.jpeg', '.webp')
    image_files = [
        filename for filename in os.listdir(folder_path)
        if filename.lower().endswith(supported_extensions)
    ]

    results = {}

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = {
            executor.submit(extract_text_from_image, model, os.path.join(folder_path, filename), filename): filename
            for filename in image_files
        }

        for future in as_completed(futures):
            filename, result = future.result()
            print(f"Completed: {filename}")
            results[filename] = result

    if output_json_path:
        with open(output_json_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=4, ensure_ascii=False)

    return results

# ========== RUN THE SCRIPT ==========
api_key = GEMINI_API_KEY
folder = r"C:\Users\<USER>\Desktop\swaraj\uploads\images"
output_json = "extracted_texts.json"

results = extract_text_from_images_in_folder_parallel(folder, api_key, output_json_path=output_json)

# ========== PARSE JSON BLOCK ==========
def extract_full_json_block(text):
    start = text.find('{')
    end = text.rfind('}')
    if start != -1 and end != -1 and end > start:
        return text[start:end+1]
    return None

images_json = []
for file, text in results.items():
    try:
        result = extract_full_json_block(text)
        json_obj = json.loads(result)
        images_json.append(json_obj)
    except Exception as e:
        print(f"Failed to parse JSON from {file}: {e}")




def extract_text_simple_gemini_psw(pdf_path, api_key):
    """
    Simple text extraction using Gemini Flash 1.5 by uploading PDF directly
   
    Args:
        pdf_path (str): Path to the PDF file
        api_key (str): Your Google Gemini API key
       
    Returns:
        str: All extracted text from the PDF
    """
    try:
        # Configure Gemini
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel('gemini-2.5-flash')
       
        print(f"Uploading PDF: {pdf_path}")
       
        # Upload PDF directly to Gemini
        uploaded_file = genai.upload_file(
            path=pdf_path,
            mime_type="application/pdf"
        )
       
        print("Extracting text...")
       
        # Extract text using Gemini
        response = model.generate_content([
            uploaded_file,
                    """Extract folowwing information from this image. Return Dictionary format along with these details.
                        /{
                        "part_name": "",
                        "customer_part_number": "",
                        "org_part_number":"",
                        "engineering_change_level": "",
                        "date": "",
                        "purchase_order_no": "",  # Shouldn't be blank
                        "weight": "",
                        "checking_aid_no": "",
                        "checking_aid_engineering_change_level": "",
                        "organization_manufacturing_info": {
                            "vendor_name": "",
                            "vendor_code": "",
                            "vendor_address": ""
                        },
                        "customer_submission_info": "",
                        "po_buyer_name": "",
                        "reason_for_submission": "",  # Shouldn't be blank
                        "level_of_submission": "",
                        "psw_signed_by_supplier": , # False or True
                        "production_rate": "" 

                        /}
                    """
        ])
       
        # Clean up uploaded file
        genai.delete_file(uploaded_file.name)
       
        if response.text:
            return response.text.strip()
        else:
            return "No text extracted"
           
    except Exception as e:
        print(f"Error: {e}")
        return None
    
pdf_file = r"C:\Users\<USER>\Desktop\swaraj\uploads\psw\01. PSW - Part Submission Warrant.pdf"

text1 = extract_text_simple_gemini_psw(pdf_file, GEMINI_API_KEY)

PSW = extract_and_parse_json(text1)
PSW



#--------------------------------------------------LLM CALL And Prompt for Process Flow Diagram-----------------------------------------

def extract_text_simple_gemini_pfd(pdf_path, api_key):
    """
    Simple text extraction using Gemini Flash 1.5 by uploading PDF directly
   
    Args:
        pdf_path (str): Path to the PDF file
        api_key (str): Your Google Gemini API key
       
    Returns:
        str: All extracted text from the PDF
    """
    try:
        # Configure Gemini
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel('gemini-2.5-flash')
       
        print(f"Uploading PDF: {pdf_path}")
       
        # Upload PDF directly to Gemini
        uploaded_file = genai.upload_file(
            path=pdf_path,
            mime_type="application/pdf"
        )
       
        print("Extracting text...")
       
        # Extract text using Gemini
        response = model.generate_content([
            uploaded_file,
                    """Extract folowwing information from this image. Return Dictionary format along with these details.
                        Details (Supplier name, Supplier Code, Part Number, Part Name and Latest Alteration)
                        Check all Child parts from BOM table AND NHUMBER OF EVERY PROCESS EXAMOPLE: 
                        CUTTING : 10

                        we want output like this:-
                        Example:-
                          {
                                "Supplier Information": {
                                    "Supplier Name": "JAYCEE STRIPS AND FASTENERS PVT. LTD.",
                                    "Supplier Code": "DDJ00202AA",
                                    "Part Name": "LOWER LINK ASSY.",
                                    "Part Number": "P80009246A",
                                    "Drg. Latest Alteration": "A,001;29.05.2024"
                                },
                                "Process Flow Details": {
                                    "FLAT": {
                                        "Processes": {
                                            "CUTTING": "10",
                                            "1st SIDE CHAMFER GRINDING": "20",
                                            "2nd SIDE CHAMFER GRINDING": "30",
                                            "VMC DRILLING": "40",
                                            "CHAMFERING": "50",
                                            "PRE-HEATING & BENDING 1": "60",
                                            "PRE-HEATING & BENDING-2": "70",
                                            "HARDENING & TEMPERING": "80"
                                        }
                                    },
                                    "BALL END-1": {
                                        "Processes": {
                                            "BOUGHT OUT PART (FORGING)": "90a",
                                            "ROUGH DRILLING": "90",
                                            "COPY TURNING": "100",
                                            "TAPER TURNING": "110"
                                        }
                                    }
                                }
                            },

                    """
        ])
       
        # Clean up uploaded file
        genai.delete_file(uploaded_file.name)
       
        if response.text:
            return response.text.strip()
        else:
            return "No text extracted"
           
    except Exception as e:
        print(f"Error: {e}")
        return None
    

#--------------------------------------------------Extracting data of Process Flow Diagram-----------------------------------------

pfd = extract_text_simple_gemini_pfd(r"C:\Users\<USER>\Desktop\swaraj\files\PFD - P80009246A.pdf", GEMINI_API_KEY)
pfd_result = extract_and_parse_json(pfd)
pfd_result


# Example 1:

# Input:
# Supplier Name: ABC INDUSTRIES  
# Supplier Code: XY123456Z  
# Part Number: ABC1234  
# Part Name: CONTROL ARM  
# Latest Alteration: B,002;15.04.2023  

# Child Parts and Processes:
# - SHAFT: (a)
#   1. CUTTING - 10
#   2. TURNING - 20
#   3. MILLING - 30

# - NUT: (b)
#   1. FORGING - 40a
#   2. THREADING - 50

# - FINAL ASSY: (c)
#   1. SHAFT + NUT WELDING - 60
#   2. PAINTING - 70

# Output:
# {
#   "Supplier Information": {
#     "Supplier Name": "ABC INDUSTRIES",
#     "Supplier Code": "XY123456Z",
#     "Part Number": "ABC1234",
#     "Part Name": "CONTROL ARM",
#     "Latest Alteration": "B,002;15.04.2023"
#   },
#   "Child Parts and Processes": {
#     "SHAFT": {
#       "Part Type": "(a)",
#       "Processes": {
#         "CUTTING": "10",
#         "TURNING": "20",
#         "MILLING": "30"
#       }
#     },
#     "NUT": {
#       "Part Type": "(b)",
#       "Processes": {
#         "FORGING": "40a",
#         "THREADING": "50"
#       }
#     },
#     "FINAL ASSY": {
#       "Part Type": "(c)",
#       "Processes": {
#         "SHAFT + NUT WELDING": "60",
#         "PAINTING": "70"
#       }
#     }
#   }
# }


# # PFMEA


# --------------------------------------------------Extraction of PFMEA Dataframe---------------------------------------
from openpyxl import load_workbook
import pandas as pd

# ✅ Load the workbook with data_only=True to get computed values instead of formulas
wb = load_workbook(r"C:\Users\<USER>\Desktop\swaraj\uploads\pfmea\PFMEA LOWER LINK.xlsx", data_only=True)
ws = wb.active  # Or use wb["SheetName"] if needed

# Handle merged cells
data = []
for row in ws.iter_rows():
    row_data = []
    for cell in row:
        if cell.coordinate in ws.merged_cells:
            # If it's a merged cell, get value from the top-left cell of the merged range
            for merged_range in ws.merged_cells.ranges:
                if cell.coordinate in merged_range:
                    cell = ws.cell(merged_range.min_row, merged_range.min_col)
                    break
        row_data.append(cell.value)
    data.append(row_data)

# Create DataFrame
pfmea_df = pd.DataFrame(data)

# Set first row as header
pfmea_df.columns = pfmea_df.iloc[0]
pfmea_df = pfmea_df.drop(index=0).reset_index(drop=True)

# --------------------------------------------------Creating PFMEA Json---------------------------------------

pfmea_validation_criteria = {
    "part_info": {
        "part_number": "",
        "item_name": ""
    },
    "bom_child_list": {},
    "dataframe": {},
}


# --------------------------------------------------Extracting Part Number and Item Name---------------------------------------
import re
for index, row in pfmea_df.iterrows():
    for j in row:
        if "Part No" in str(j):
            print(str(j).split(" ")[-1])
            pfmea_validation_criteria["part_info"]["part_number"] = str(j).split(" ")[-1]
        if 'Item Name' in str(j):
            print(str(j).split(":  ")[-1])
            pfmea_validation_criteria["part_info"]["item_name"] = str(j).split(":  ")[-1]
    pattern = r"^\([a-z]\)$"
    if re.search(pattern, str(row[0])):  # check match in first column
        print(row[1]) 
        print() # print second column
        pfmea_validation_criteria["bom_child_list"][row[1]]=index

# --------------------------------------------------Extracting Column Names---------------------------------------

first_key = list(pfmea_validation_criteria['bom_child_list'])[0]
print(pfmea_validation_criteria['bom_child_list'][first_key])
column_no = pfmea_validation_criteria['bom_child_list'][first_key]-1
column = pfmea_df.iloc[column_no].tolist()[:18]  # ✅ Use .iloc
print(column)


# --------------------------------------------------Extracting Dataframes---------------------------------------


com_child_list =list(pfmea_validation_criteria['bom_child_list'])
for i in range(len(com_child_list)):
    print(com_child_list[i])
    print(pfmea_validation_criteria['bom_child_list'][com_child_list[i]])
    if i == len(com_child_list)-1:
        temp_df = pfmea_df.iloc[pfmea_validation_criteria['bom_child_list'][com_child_list[i]]+1:pfmea_validation_criteria['bom_child_list'][com_child_list[i]]+4,:18]
        # break
    else:
        temp_df = pfmea_df.iloc[pfmea_validation_criteria['bom_child_list'][com_child_list[i]]+1:pfmea_validation_criteria['bom_child_list'][com_child_list[i+1]],:18]
    temp_df.columns = column
    pfmea_validation_criteria['dataframe'][com_child_list[i]] = temp_df
    print(temp_df)
    


# --------------------------------------------------Extraction of PFMEA Dataframe---------------------------------------
from openpyxl import load_workbook

# Load the workbook and sheet
wb = load_workbook(r"C:\Users\<USER>\Desktop\swaraj\uploads\cp\15.Control plan .xlsx" , data_only=True)
ws = wb.active  # or wb["Sheet1"]

# Create a matrix to store values, handling merged cells
data = []
for row in ws.iter_rows():
    row_data = []
    for cell in row:
        if cell.coordinate in ws.merged_cells:
            # If it's a merged cell, get value from the top-left cell of the merged range
            for merged_range in ws.merged_cells.ranges:
                if cell.coordinate in merged_range:
                    cell = ws.cell(merged_range.min_row, merged_range.min_col)
                    break
        row_data.append(cell.value)
    data.append(row_data)


control_df = pd.DataFrame(data)

# Optionally set the first row as column headers (if needed)
control_df.columns = control_df.iloc[0]
control_df = control_df.drop(index=0).reset_index(drop=True)

# --------------------------------------------------Creating PFMEA Json---------------------------------------

control_data = {
    "part_info": {
        "part_number": "",
        "part_name": "",
        },
    "bom_child_list": {},
    "dataframe": {},
}

# --------------------------------------------------Extracting Part Number and Item Name---------------------------------------
import re
for index, row in control_df.iterrows():
    for j in row:
        if "Part Number" in str(j):
            print(str(j).split(". ")[-1])
            control_data["part_info"]["part_number"] = str(j).split(" ")[-1]
        if 'Part Name' in str(j):
            print(str(j).split(".  ")[-1])
            control_data["part_info"]["part_name"] = str(j).split(".  ")[-1]
    pattern = r"^\([a-z]\)$"
    if re.search(pattern, str(row[0])):  # check match in first column
        print(row[1]) 
        print() # print second column
        control_data["bom_child_list"][row[1]]=index

# --------------------------------------------------Extracting Column Names---------------------------------------

first_key = list(control_data['bom_child_list'])[0]
print(control_data['bom_child_list'][first_key])
column_no = control_data['bom_child_list'][first_key]-1
column = control_df.iloc[column_no].tolist()[:13]  # ✅ Use .iloc
column = ['Responsibility' if val == None else val for val in column]
print(column)

# --------------------------------------------------Extracting Dataframes---------------------------------------


com_child_list =list(control_data['bom_child_list'])
for i in range(len(com_child_list)):
    print(com_child_list[i])
    print(control_data['bom_child_list'][com_child_list[i]])
    if i == len(com_child_list)-1:
        temp_df = control_df.iloc[control_data['bom_child_list'][com_child_list[i]]+1:control_data['bom_child_list'][com_child_list[i]]+6,:13]
        break
    else:
        temp_df = control_df.iloc[control_data['bom_child_list'][com_child_list[i]]+1:control_data['bom_child_list'][com_child_list[i+1]],:13]
    temp_df.columns = column
    control_data['dataframe'][com_child_list[i]] = temp_df
    print(temp_df)
    



checks={}
mismatch_checks={}



from datetime import datetime

def parse_date(date_str):
    """Try parsing known date formats"""
    for fmt in ("%d-%b-%y", "%d-%m-%y"):
        try:
            return datetime.strptime(date_str.strip(), fmt)
        except ValueError:
            
            continue
    raise ValueError(f"Unrecognized date format: {date_str}")





def get_key_ignore_case(d, target_key):
    """
    Returns the value for a key in the dictionary `d` that matches `target_key` case-insensitively.
    Returns None if the key doesn't exist.
    """
    for k, v in d.items():
        if k.lower() == target_key.lower():
            return v
    return None


obj=[]
for i in images_json:
    print(i)
    if i['table_type']=='TYPE A':
        for j in i['data']:
            if j['DRG. No.']==PSW['customer_part_number']:
                checks['customer_part_number']={'psw':PSW['customer_part_number'],'image':j['DRG. No.'], 
                                       'condition':True}
                obj.append(j)

    if i['table_type']=='TYPE B':
        if PSW['customer_part_number']==i['data']['DRAWING NO.']:
            checks['customer_part_number']={'psw':PSW['customer_part_number'],'image':i['data']['DRAWING NO.'], 
                                       'condition':True}
            obj.append(i['data'])


for i in obj:
    if i['DRAWING NO.'].lower()==PSW['org_part_number'].lower():
        checks['part_number']={'psw':PSW['org_part_number'],'image':i['DRAWING NO.'], 'condition':True}
    else:
        checks['part_number']={'psw':PSW['org_part_number'],'image':i['DRAWING NO.'], 'condition':False}
        mismatch_checks['part_number']={'psw':PSW['org_part_number'],'image':i['DRAWING NO.']}

    if i['PART'].lower() in PSW['part_name'].lower():
        # print(i['PART'].lower(),PSW['part_name'].lower())
        checks['Part_name']={'psw':PSW['part_name'],'image':i['PART'], 'condition':True}
    else:
        print(i['PART'].lower(),PSW['part_name'].lower())
        checks['Part_name']={'psw':PSW['part_name'],'image':i['PART'], 'condition':False}
        mismatch_checks['Part_name']={'psw':PSW['part_name'],'image':i['PART']}

    if (i['REV']=='001') and (PSW['engineering_change_level']=='A'):
        checks['engineering_change_level']={'psw':PSW['engineering_change_level'],'image':i['REV'], 'condition':True}
    else:
        checks['engineering_change_level']={'psw':PSW['engineering_change_level'],'image':i['REV'], 'condition':False}
        mismatch_checks['engineering_change_level']={'psw':PSW['engineering_change_level'],'image':i['REV']}

    if i['WEIGHT']==PSW['weight']:
        checks['weight']={'psw':PSW['weight'],'image':i['WEIGHT'], 'condition':True}
    else:
        checks['weight']={'psw':PSW['weight'],'image':i['WEIGHT'], 'condition':False}
        mismatch_checks['weight']={'psw':PSW['weight'],'image':i['WEIGHT']}
    try:
        i_date = parse_date(i.get('DATE', ''))
        psw_date = parse_date(PSW.get('date', ''))
        if i_date.date() == psw_date.date():
            checks['date'] = {'psw':PSW['date'],'image':i['DATE'], 'condition':True}
        else:
            checks['date'] = {'psw':PSW['date'],'image':i['DATE'], 'condition':False}
            mismatch_checks['date']={'psw':PSW['date'],'image':i['DATE']}
    except ValueError as e:
        print("Date format error:", e)

if PSW['purchase_order_no'] != None:
    checks['purchase_order_no']={'psw':PSW['purchase_order_no'],'condition':True}
else:
    checks['purchase_order_no']={'psw':PSW['purchase_order_no'], 'condition':False}
    mismatch_checks['purchase_order_no']={'psw':PSW['purchase_order_no'], 'reason':'isNone'}

if PSW['customer_submission_info'] in (['MAHINDRA & MAHINDRA LTD.','SWARAJ', 'M&M']):
    checks['customer_submission_info']={'psw':PSW['customer_submission_info'],'condition':True}
else:
    checks['customer_submission_info']={'psw':PSW['customer_submission_info'],'condition':False}
    mismatch_checks['customer_submission_info']={'psw':PSW['customer_submission_info'],'reason':'notInList'}

# if PSW['po_buyer_name']=='MR. VARUN KUMAR':
#     checks['po_buyer_name']={'psw':PSW['po_buyer_name'],'condition':True}
# else:
#     checks['po_buyer_name']={'psw':PSW['po_buyer_name'],'condition':False}
#     mismatch_checks['po_buyer_name']={'psw':PSW['po_buyer_name'],'reason':'notEqual'}

if PSW['reason_for_submission'] != None:
    checks['reason_for_submission']={'psw':PSW['reason_for_submission'],'condition':True}
else:
    checks['reason_for_submission']={'psw':PSW['reason_for_submission'],'condition':False}
    mismatch_checks['reason_for_submission']={'psw':PSW['reason_for_submission'],'reason':'isNone'}

if 'level 3' in PSW['level_of_submission'].lower():
    checks['level_of_submission']={'psw':PSW['level_of_submission'],'condition':True}
else:
    checks['level_of_submission']={'psw':PSW['level_of_submission'],'condition':False}
    mismatch_checks['level_of_submission']={'psw':PSW['level_of_submission'],'reason':'notEqual'}

if PSW['psw_signed_by_supplier']==True:
    checks['psw_signed_by_supplier']={'psw':PSW['psw_signed_by_supplier'],'condition':True}
else:
    checks['psw_signed_by_supplier']={'psw':PSW['psw_signed_by_supplier'],'condition':False}
    mismatch_checks['psw_signed_by_supplier']={'psw':PSW['psw_signed_by_supplier'],'reason':'notSigned'}

if PSW['production_rate']!=None:
    checks['production_rate']={'psw':PSW['production_rate'],'condition':True}
else:
    checks['production_rate']={'psw':PSW['production_rate'],'condition':False}
    mismatch_checks['production_rate']={'psw':PSW['production_rate'],'reason':'isNone'}



if pfd_result['Supplier Information']['Part Number']==PSW['customer_part_number']:
    checks['pfd_part_number']={'psw':PSW['customer_part_number'],'pfd':pfd_result['Supplier Information']['Part Number'], 'condition':True}
else:
    checks['pfd_part_number']={'psw':PSW['customer_part_number'],'pfd':pfd_result['Supplier Information']['Part Number'], 'condition':False}
    mismatch_checks['pfd_part_number']={'psw':PSW['customer_part_number'],'pfd':pfd_result['Supplier Information']['Part Number']}
    
if pfd_result['Supplier Information']['Part Name']==PSW['part_name']:
    checks['pfd_part_name']={'psw':PSW['part_name'],'pfd':pfd_result['Supplier Information']['Part Name'], 'condition':True}
else:
    checks['pfd_part_name']={'psw':PSW['part_name'],'pfd':pfd_result['Supplier Information']['Part Name'], 'condition':False}
    mismatch_checks['pfd_part_name']={'psw':PSW['part_name'],'pfd':pfd_result['Supplier Information']['Part Name']}

if pfd_result['Supplier Information']['Supplier Name']==PSW['organization_manufacturing_info']['vendor_name']:
    checks['pfd_supplier_name']={'psw':PSW['organization_manufacturing_info']['vendor_name'],'pfd':pfd_result['Supplier Information']['Supplier Name'], 'condition':True}
else:
    checks['pfd_supplier_name']={'psw':PSW['organization_manufacturing_info']['vendor_name'],'pfd':pfd_result['Supplier Information']['Supplier Name'], 'condition':False}
    mismatch_checks['pfd_supplier_name']={'psw':PSW['organization_manufacturing_info']['vendor_name'],'pfd':pfd_result['Supplier Information']['Supplier Name']}

if pfd_result['Supplier Information']['Supplier Code']==PSW['organization_manufacturing_info']['vendor_code']:
    checks['pfd_supplier_code']={'psw':PSW['organization_manufacturing_info']['vendor_code'],'pfd':pfd_result['Supplier Information']['Supplier Code'], 'condition':True}
else:
    checks['pfd_supplier_code']={'psw':PSW['organization_manufacturing_info']['vendor_code'],'pfd':pfd_result['Supplier Information']['Supplier Code'], 'condition':False}
    mismatch_checks['pfd_supplier_code']={'psw':PSW['organization_manufacturing_info']['vendor_code'],'pfd':pfd_result['Supplier Information']['Supplier Code']}

if PSW['engineering_change_level'] in pfd_result['Supplier Information']['Drg. Latest Alteration']:
    checks['pfd_engineering_change_level']={'psw':PSW['engineering_change_level'], 'condition':True}
else:
    checks['pfd_engineering_change_level']={'psw':PSW['engineering_change_level'], 'condition':False}
    mismatch_checks['pfd_engineering_change_level']={'psw':PSW['engineering_change_level'], 'reason':'notInPFD'}


for i in images_json:
    if i['table_type']=='TYPE A':
        for j in i['data']:
            print(j['Description'])
            if j['Description'] in pfd_result['Process Flow Details']:
                checks[f"pfd_{j['Description']}"]={'image':j['Description'],'condition':True}
            else:
                checks[f"pfd_{j['Description']}"]={'image':j['Description'],'condition':False}
                mismatch_checks[f"pfd_{j['Description']}"]={'image':j['Description'],'reason':'notInPFD'}




if pfmea_validation_criteria['part_info']['part_number']==PSW['customer_part_number']:
    checks['pfmea_part_number']={'psw':PSW['customer_part_number'],'pfmea':pfmea_validation_criteria['part_info']['part_number'], 'condition':True}
else:
    checks['pfmea_part_number']={'psw':PSW['customer_part_number'],'pfmea':pfmea_validation_criteria['part_info']['part_number'], 'condition':False}
    mismatch_checks['pfmea_part_number']={'psw':PSW['customer_part_number'],'pfmea':pfmea_validation_criteria['part_info']['part_number']}

if pfmea_validation_criteria['part_info']['item_name']==PSW['part_name']:
    checks['pfmea_part_name']={'psw':PSW['part_name'],'pfmea':pfmea_validation_criteria['part_info']['item_name'], 'condition':True}
else:
    checks['pfmea_part_name']={'psw':PSW['part_name'],'pfmea':pfmea_validation_criteria['part_info']['item_name'], 'condition':False}
    mismatch_checks['pfmea_part_name']={'psw':PSW['part_name'],'pfmea':pfmea_validation_criteria['part_info']['item_name']}

for i in pfmea_validation_criteria['bom_child_list']:
    # print(j['Description'])
    if i in pfd_result['Process Flow Details']:
        checks[f"pfmea__{i}"]={'pfmea':i,'condition':True}
    else:
        checks[f"pfmea__{i}"]={'pfmea':i,'condition':False}
        mismatch_checks[f"pfmea__{i}"]={'pfmea':i,'reason':'notInPFD'}

for i in images_json:
    if i['table_type']=='TYPE A':
        for j in i['data']:
            if j['Description'] in pfmea_validation_criteria['bom_child_list']:
                checks[f"pfmea_bom_{j['Description']}"]={'image':j['Description'],'condition':True}
            else:
                checks[f"pfmea_bom_{j['Description']}"]={'image':j['Description'],'condition':False}
                mismatch_checks[f"pfmea_{j['Description']}_"]={'image':j['Description'],'reason':'notInPFMEA'}

# =============================================================================>>>>>>>
# =============================================================================>>>>>>>
# =============================================================================>>>>>>>
# =============================================================================>>>>>>>
# =============================================================================>>>>>>>
# =============================================================================>>>>>>>
# =============================================================================>>>>>>>
# =============================================================================>>>>>>>
# =============================================================================>>>>>>>
# =============================================================================>>>>>>>
# =============================================================================>>>>>>>
# =============================================================================>>>>>>>
# =============================================================================>>>>>>>
# =============================================================================>>>>>>>
# =============================================================================>>>>>>>
# =============================================================================>>>>>>>
# =============================================================================>>>>>>>
# =============================================================================>>>>>>>
# =============================================================================>>>>>>>
# =============================================================================>>>>>>>
# =============================================================================>>>>>>>
# =============================================================================>>>>>>>
# =============================================================================>>>>>>>

import re

def normalize(text):
    # Normalize by removing dashes, newlines, extra spaces and lowering case
    text = text.lower()
    text = re.sub(r'\band\b|&', ',', text)  # Replace 'and' or '&' with comma
    text = re.sub(r'[\s,]+', ' ', text)     # Normalize whitespace and commas
    text = text.replace('tacking', 'tack')  # Simplify common word variants
    text = text.replace('welding', 'weld')
    return text.strip()

for i in pfd_result['Process Flow Details']:
    for j in pfmea_validation_criteria['dataframe']:
        if i == j:
            # Get unique PFMEA function list and normalize them
            pfmea_df = pfmea_validation_criteria['dataframe'][j]
            pfmea_func_unique = pfmea_df['PROCESS FUNCTIONAL REQUIRMENT'].unique()
            pfmea_func_normalized_map = {
                normalize(x): x for x in pfmea_func_unique
            }
            # print(pfmea_func_normalized_map)
            for idx, k in enumerate(pfd_result['Process Flow Details'][i]['Processes']):
                norm_k = normalize(k)

                # print("norm_k",norm_k,k)
                if norm_k in pfmea_func_normalized_map:
                    matched_func = pfmea_func_normalized_map[norm_k]
                    # print(matched_func)
                    # Get S.NO from PFMEA df where functional requirement matches
                    pfmea_row = pfmea_df[['S.NO', 'PROCESS FUNCTIONAL REQUIRMENT']].drop_duplicates()
                    pfmea_sno = pfmea_row[pfmea_row['PROCESS FUNCTIONAL REQUIRMENT'] == matched_func]['S.NO'].values[0]
                    # print(pfmea_sno)
                    if int(pfd_result['Process Flow Details'][i]['Processes'][k]) == int(pfmea_sno):
                        checks[f'pfmea_{i}_{k}']={'pfd':pfd_result['Process Flow Details'][i]['Processes'][k],'pfmea':pfmea_sno, 'condition':True}
                    else:
                        # print(f"⚠ Step mismatch for: {k} | PFD Step: {pfd_result['Process Flow Details'][i]['Processes'][k]}, PFMEA S.NO: {pfmea_sno}")
                        checks[f'pfmea_{i}_{k}']={'pfd':pfd_result['Process Flow Details'][i]['Processes'][k],'pfmea':pfmea_sno, 'condition':False}
                        mismatch_checks[f'pfmea_{i}_']={'pfd':pfd_result['Process Flow Details'][i]['Processes'][k],'pfmea':pfmea_sno, 'reason':'stepMismatch'}
                else:
                    checks[f'pfmea_{i}_{k}']={'pfd':pfd_result['Process Flow Details'][i]['Processes'][k], 'condition':False}
                    mismatch_checks[f'pfmea_{i}_{k}']={'pfd':pfd_result['Process Flow Details'][i]['Processes'][k], 'reason':'notInPFMEA'}

flag = 0
for i in pfmea_validation_criteria['dataframe']:
    if pfmea_validation_criteria['dataframe'][i][pfmea_validation_criteria['dataframe'][i]['POTENTIAL EFFECT MODE'].isna()].size>0:
        checks[f'pfmea_POTENTIAL EFFECT MODE__missingData']={'condition':True}
        mismatch_checks[f'pfmea_POTENTIAL EFFECT MODE_missingData_{i}']={'reason':'missingData'}
        flag = 1
        break
if flag == 0:
    checks['pfmea_POTENTIAL EFFECT MODE_missingData']={'condition':False}
    


flag = 0
for i in pfmea_validation_criteria['dataframe']:
    if pfmea_validation_criteria['dataframe'][i][pfmea_validation_criteria['dataframe'][i]['POTENTIAL EFFECT MODE'].isna()].size>0:
        checks[f'pfmea_POTENTIAL EFFECT MODE_missingData']={'condition':True}
        mismatch_checks[f'pfmea_POTENTIAL EFFECT MODE_missingData_{i}']={'reason':'missingData'}
        flag = 1
        break
if flag == 0:
    checks['pfmea_POTENTIAL EFFECT MODE_missingData']={'condition':False}

flag = 0
for i in pfmea_validation_criteria['dataframe']:
    if pfmea_validation_criteria['dataframe'][i][pfmea_validation_criteria['dataframe'][i]['S E V'].isna()].size>0:
        checks[f'pfmea_S E V_missingData']={'condition':True}
        mismatch_checks[f'pfmea_S E V_missingData_{i}']={'reason':'missingData'}
        flag = 1
        break
if flag == 0:
    checks['pfmea_S E V_missingData']={'condition':False}

flag = 0
for i in pfmea_validation_criteria['dataframe']:
    df = pfmea_validation_criteria['dataframe'][i]
    sev_filtered = df[df['S E V'] > 8]
    if sev_filtered.empty:
        checks[f'pfmea_S E V_POKA and FP_missing']={'condition':False}
        flag = 1
        break
    # Check if either column contains "POKA YOKO" or "Fool Proofing"
    has_poka_or_fp = (
        sev_filtered['CURRENT PROCESS CONTROLS PREVENTION'].astype(str).str.contains("POKA YOKO", case=False, na=False) |
        sev_filtered['CURRENT PROCESS CONTROLS DETECTION'].astype(str).str.contains("POKA YOKO", case=False, na=False) |
        sev_filtered['CURRENT PROCESS CONTROLS PREVENTION'].astype(str).str.contains("Fool Proofing", case=False, na=False) |
        sev_filtered['CURRENT PROCESS CONTROLS DETECTION'].astype(str).str.contains("Fool Proofing", case=False, na=False)
    )
    if ~has_poka_or_fp.any():
        checks[f'pfmea_S E V_POKA and FP_missing']={'condition':True}
        mismatch_checks[f'pfmea_S E V_POKA and FP_missing_{i}']={'reason':'missingData'}
        flag = 1
        break
    else:
        checks[f'pfmea_S E V_POKA and FP_missing']={'condition':False}

flag = 0
for i in pfmea_validation_criteria['dataframe']:
    if pfmea_validation_criteria['dataframe'][i][pfmea_validation_criteria['dataframe'][i]['CURRENT PROCESS CONTROLS PREVENTION'].isna()].size>0:
        checks[f'pfmea_CURRENT PROCESS CONTROLS PREVENTION_missingData']={'condition':True}
        mismatch_checks[f'pfmea_CURRENT PROCESS CONTROLS PREVENTION_missingData_{i}']={'reason':'missingData'}
        flag = 1
        break
if flag == 0:
    checks['pfmea_CURRENT PROCESS CONTROLS PREVENTION_missingData']={'condition':False}

flag = 0
for i in pfmea_validation_criteria['dataframe']:
    flat_df = pfmea_validation_criteria['dataframe'][i].copy()

    # Clean column names (optional but safer)
    flat_df.columns = ['S.NO', 'PROCESS FUNCTIONAL REQUIRMENT', 'POTENTIAL FAILURE MODE', 'POTENTIAL EFFECT MODE',
                       'S E V', 'POTENTIAL CAUSES', 'CURRENT PROCESS CONTROLS PREVENTION', 'OCCUR',
                       'CURRENT PROCESS CONTROLS DETECTION', 'DETEC', 'R     P    N', 'RECOMMENDED ACTION',
                       'RESPONSIBILITY & TARGET DATE', 'ACTION TAKEN', 'SEV_', 'OCCUR_', 'DETEC_', 'RPN_']
    if flat_df['OCCUR_'].isna().any():
        checks[f'pfmea_OCCUR_missingData']={'condition':True}
        mismatch_checks[f'pfmea_OCCUR_missingData_{i}']={'reason':'missingData'}
        print(i)
        flag = 1
        break
if flag == 0:
    checks['pfmea_OCCUR_missingData']={'condition':False}

flag = 0
for i in pfmea_validation_criteria['dataframe']:
    if pfmea_validation_criteria['dataframe'][i][pfmea_validation_criteria['dataframe'][i]['CURRENT PROCESS CONTROLS DETECTION'].isna()].size>0:
        checks[f'pfmea_CURRENT PROCESS CONTROLS DETECTION_missingData']={'condition':True}
        mismatch_checks[f'pfmea_CURRENT PROCESS CONTROLS DETECTION_missingData_{i}']={'reason':'missingData'}
        flag = 1
        break
if flag == 0:
    checks['pfmea_CURRENT PROCESS CONTROLS DETECTION_missingData']={'condition':False}

flag = 0
for i in pfmea_validation_criteria['dataframe']:
    flat_df = pfmea_validation_criteria['dataframe'][i].copy()

    # Clean column names (optional but safer)
    flat_df.columns = ['S.NO', 'PROCESS FUNCTIONAL REQUIRMENT', 'POTENTIAL FAILURE MODE', 'POTENTIAL EFFECT MODE',
                       'S E V', 'POTENTIAL CAUSES', 'CURRENT PROCESS CONTROLS PREVENTION', 'OCCUR',
                       'CURRENT PROCESS CONTROLS DETECTION', 'DETEC', 'R     P    N', 'RECOMMENDED ACTION',
                       'RESPONSIBILITY & TARGET DATE', 'ACTION TAKEN', 'SEV_', 'OCCUR_', 'DETEC_', 'RPN_']
    if flat_df['DETEC_'].isna().any():
        checks[f'pfmea_DETEC_missingData']={'condition':True}
        mismatch_checks[f'pfmea_DETEC_missingData_{i}']={'reason':'missingData'}
        print(i)
        flag = 1
        break
if flag == 0:
    checks['pfmea_DETEC_missingData']={'condition':False}

flag = 0

for i in pfmea_validation_criteria['dataframe']:
    flat_df = pfmea_validation_criteria['dataframe'][i].copy()

    # Clean column names (optional but safer)
    flat_df.columns = ['S.NO', 'PROCESS FUNCTIONAL REQUIRMENT', 'POTENTIAL FAILURE MODE', 'POTENTIAL EFFECT MODE',
                       'S E V', 'POTENTIAL CAUSES', 'CURRENT PROCESS CONTROLS PREVENTION', 'OCCUR',
                       'CURRENT PROCESS CONTROLS DETECTION', 'DETEC', 'R     P    N', 'RECOMMENDED ACTION',
                       'RESPONSIBILITY & TARGET DATE', 'ACTION TAKEN', 'SEV_', 'OCCUR_', 'DETEC_', 'RPN_']

    # Convert necessary columns to numeric
    for col in ['SEV_', 'OCCUR_', 'DETEC_', 'RPN_']:
        flat_df[col] = pd.to_numeric(flat_df[col], errors='coerce')

    # Recalculate RPN
    calculated_rpn = flat_df['SEV_'] * flat_df['OCCUR_'] * flat_df['DETEC_']

    # Check for mismatches (use parentheses with ~)
    mismatched = (calculated_rpn != flat_df['RPN_']) & (~flat_df['RPN_'].isna())

    if mismatched.any():
        checks[f'{i}_pfmea_RPN_Multiplication_data_failed'] = {'condition':True}
        mismatch_checks[f'pfmea_RPN_Multiplication_data_failed_{i}'] = {'reason':'RPN_Multiplication_data_failed'}
        print(f"❌ RPN mismatch in: {i}")
        flag = 1
        break
    else:
        checks[f'{i}_pfmea_RPN_Multiplication_data_failed'] = {'condition':False}

if flag == 0:
    checks['pfmea_RPN_Multiplication_data_failed'] = {'condition':False}

flag = 0

for i in pfmea_validation_criteria['dataframe']:
    flat_df = pfmea_validation_criteria['dataframe'][i].copy()

    # Clean column names (optional but safer)
    flat_df.columns = ['S.NO', 'PROCESS FUNCTIONAL REQUIRMENT', 'POTENTIAL FAILURE MODE', 'POTENTIAL EFFECT MODE',
                       'S E V', 'POTENTIAL CAUSES', 'CURRENT PROCESS CONTROLS PREVENTION', 'OCCUR',
                       'CURRENT PROCESS CONTROLS DETECTION', 'DETEC', 'R     P    N', 'RECOMMENDED ACTION',
                       'RESPONSIBILITY & TARGET DATE', 'ACTION TAKEN', 'SEV_', 'OCCUR_', 'DETEC_', 'RPN_']
    for col in ['SEV_', 'OCCUR_', 'DETEC_', 'RPN_']:
        flat_df[col] = pd.to_numeric(flat_df[col], errors='coerce')
    if flat_df['RECOMMENDED ACTION'][(flat_df['RPN_'])>100].isna().any():
        checks[f'{i}_pfmea_RECOMMENDED ACTION_>100_missingData'] = {'condition':True}
        mismatch_checks[f'pfmea_RECOMMENDED ACTION_>100_missingData_{i}'] = {'reason':'missingData'}
        flag = 1
        # break
    else:
        checks[f'{i}_pfmea_RECOMMENDED ACTION_.>100_missingData'] = {'condition':False}

flag = 0

for i in pfmea_validation_criteria['dataframe']:
    flat_df = pfmea_validation_criteria['dataframe'][i].copy()

    # Clean column names (optional but safer)
    flat_df.columns = ['S.NO', 'PROCESS FUNCTIONAL REQUIRMENT', 'POTENTIAL FAILURE MODE', 'POTENTIAL EFFECT MODE',
                       'S E V', 'POTENTIAL CAUSES', 'CURRENT PROCESS CONTROLS PREVENTION', 'OCCUR',
                       'CURRENT PROCESS CONTROLS DETECTION', 'DETEC', 'R     P    N', 'RECOMMENDED ACTION',
                       'RESPONSIBILITY & TARGET DATE', 'ACTION TAKEN', 'SEV_', 'OCCUR_', 'DETEC_', 'RPN_']
    for col in ['SEV_', 'R     P    N', 'DETEC_', 'RPN_']:
        flat_df[col] = pd.to_numeric(flat_df[col], errors='coerce')
    if flat_df[flat_df['RPN_']>100].shape[0]>0 or flat_df[flat_df['R     P    N']>100].shape[0]>0:
        checks[f'{i}_pfmea_RPN>100_'] = {'condition':True}
        mismatch_checks[f'pfmea_RPN>100_{i}'] = {'reason':'RPN>100'}
        flag = 1
        # break
    else:
        checks[f'{i}_pfmea_RPN>100_'] = {'condition':False}

flag = 0

for i in pfmea_validation_criteria['dataframe']:
    flat_df = pfmea_validation_criteria['dataframe'][i].copy()

    # Clean column names (optional but safer)
    flat_df.columns = ['S.NO', 'PROCESS FUNCTIONAL REQUIRMENT', 'POTENTIAL FAILURE MODE', 'POTENTIAL EFFECT MODE',
                       'S E V', 'POTENTIAL CAUSES', 'CURRENT PROCESS CONTROLS PREVENTION', 'OCCUR',
                       'CURRENT PROCESS CONTROLS DETECTION', 'DETEC', 'R     P    N', 'RECOMMENDED ACTION',
                       'RESPONSIBILITY & TARGET DATE', 'ACTION TAKEN', 'SEV_', 'OCCUR_', 'DETEC_', 'RPN_']
    if flat_df['RESPONSIBILITY & TARGET DATE'].isna().any():
        checks[f'{i}_pfmea_RESPONSIBILITY & TARGET DATE_missingData'] = {'condition':True}
        mismatch_checks[f'pfmea_RESPONSIBILITY & TARGET DATE_missingData_{i}'] = {'reason':'missingData'}
        flag = 1
        # break
    else:
        checks[f'{i}_pfmea_RESPONSIBILITY & TARGET DATE_missingData'] = {'condition':False}

flag = 0

for i in pfmea_validation_criteria['dataframe']:
    flat_df = pfmea_validation_criteria['dataframe'][i].copy()

    # Clean column names (optional but safer)
    flat_df.columns = ['S.NO', 'PROCESS FUNCTIONAL REQUIRMENT', 'POTENTIAL FAILURE MODE', 'POTENTIAL EFFECT MODE',
                       'S E V', 'POTENTIAL CAUSES', 'CURRENT PROCESS CONTROLS PREVENTION', 'OCCUR',
                       'CURRENT PROCESS CONTROLS DETECTION', 'DETEC', 'R     P    N', 'RECOMMENDED ACTION',
                       'RESPONSIBILITY & TARGET DATE', 'ACTION TAKEN', 'SEV_', 'OCCUR_', 'DETEC_', 'RPN_']
    if flat_df[['OCCUR_', 'DETEC_','SEV_']][~flat_df['RESPONSIBILITY & TARGET DATE'].isna()].isna().any().any():
        checks[f'{i}_pfmea_Ocur_Detec_Sev_missingData'] = {'condition':True}
        mismatch_checks[f'pfmea_Ocur Detec Sev where RESPONSIBILITY & TARGET DATE is not null_missingData_{i}'] = {'reason':'missingData'}
        flag = 1
        # break
    else:
        checks[f'{i}_pfmea_Ocur_Detec_Sev_missingData'] = {'condition':False}



# 


if control_data['part_info']['part_number']==pfd_result['Supplier Information']['Part Number'] and control_data['part_info']['part_number']==pfmea_validation_criteria['part_info']['part_number']:
    checks['control_part_number']={'control_data':control_data['part_info']['part_number'],'pfd':pfd_result['Supplier Information']['Part Number'],'pfmea':pfmea_validation_criteria['part_info']['part_number'], 'condition':True}
else:
    checks['control_part_number']={'control_data':control_data['part_info']['part_number'],'pfd':pfd_result['Supplier Information']['Part Number'],'pfmea':pfmea_validation_criteria['part_info']['part_number'], 'condition':False}
    mismatch_checks['control_part_number']={'control_data':control_data['part_info']['part_number'],'pfd':pfd_result['Supplier Information']['Part Number'],'pfmea':pfmea_validation_criteria['part_info']['part_number']}

if control_data['part_info']['part_name']==pfd_result['Supplier Information']['Part Name'] and control_data['part_info']['part_name']==pfmea_validation_criteria['part_info']['item_name']:
    checks['control_part_name']={'control_data':control_data['part_info']['part_name'],'pfd':pfd_result['Supplier Information']['Part Name'],'pfmea':pfmea_validation_criteria['part_info']['item_name'], 'condition':True}
else:
    checks['control_part_name']={'control_data':control_data['part_info']['part_name'],'pfd':pfd_result['Supplier Information']['Part Name'],'pfmea':pfmea_validation_criteria['part_info']['item_name'], 'condition':False}
    mismatch_checks['control_part_name']={'control_data':control_data['part_info']['part_name'],'pfd':pfd_result['Supplier Information']['Part Name'],'pfmea':pfmea_validation_criteria['part_info']['item_name']}

import re

def normalize(text):
    # Normalize by removing dashes, newlines, extra spaces and lowering case
    text = text.lower()

    # Replace 'and' or '&' with comma
    text = re.sub(r'\band\b|&', ',', text)

    # Replace hyphens with a space
    text = text.replace('-', ' ')

    # Simplify common word variants
    text = text.replace('tacking', 'tack')
    text = text.replace('welding', 'weld')

    # Remove multiple spaces and commas and normalize to single space
    text = re.sub(r'[\s,]+', ' ', text)

    # Final strip to remove leading/trailing spaces
    return text.strip()

for i in pfd_result['Process Flow Details']:
    for j in control_data['dataframe']:
        if i == j:
            # Get unique PFMEA function list and normalize them
            control_temp_df = control_data['dataframe'][j]
            cp_func_unique = control_temp_df['Process Name / Operation Description.'].unique()
            cp_func_normalized_map = {
                normalize(x): x for x in cp_func_unique
            }
            # print(cp_func_normalized_map)
            for idx, k in enumerate(pfd_result['Process Flow Details'][i]['Processes']):
                norm_k = normalize(k)

                # print("norm_k",norm_k,k)
                if norm_k in cp_func_normalized_map:
                    matched_func = cp_func_normalized_map[norm_k]
                    # print(matched_func)
                    # Get Part / Process Number. from PFMEA df where functional requirement matches
                    cp_row = control_temp_df[['Part / Process Number.', 'Process Name / Operation Description.']].drop_duplicates()
                    cp_sno = cp_row[cp_row['Process Name / Operation Description.'] == matched_func]['Part / Process Number.'].values[0]
                    # print(cp_sno)
                    if int(pfd_result['Process Flow Details'][i]['Processes'][k]) == int(cp_sno):
                        checks[f'cp_{i}_{k}_']={'pfd':pfd_result['Process Flow Details'][i]['Processes'][k],'cp':cp_sno, 'condition':True}
                    else:
                        # print(f"⚠ Step mismatch for: {k} | PFD Step: {pfd_result['Process Flow Details'][i]['Processes'][k]}, PFMEA Part / Process Number.: {cp_sno}")
                        checks[f'cp_{i}_{k}_']={'pfd':pfd_result['Process Flow Details'][i]['Processes'][k],'cp':cp_sno, 'condition':False}
                        mismatch_checks[f'cp_{i}']={'pfd':pfd_result['Process Flow Details'][i]['Processes'][k],'cp':cp_sno, 'reason':'stepMismatch'}
                else:
                    checks[f'cp_{i}_{k}']={'pfd':pfd_result['Process Flow Details'][i]['Processes'][k], 'condition':False}
                    mismatch_checks[f'cp_{i}']={'pfd':pfd_result['Process Flow Details'][i]['Processes'][k], 'reason':'notInCP'}

for i in control_data['dataframe']:
    if control_data['dataframe'][i]['Process Name / Operation Description.'].isna().any():
        checks[f'cp_{i}_Process Name / Operation Description._missingData']={'condition':True}
        mismatch_checks[f'cp_{i}_Process Name / Operation Description._missingData']={'reason':'missingData'}
    else:
        checks[f'cp_{i}_Process Name / Operation Description._missingData']={'condition':False}

for i in control_data['dataframe']:
    if control_data['dataframe'][i]['Part / Process Number.'].isna().any():
        checks[f'cp_{i}_Part / Process Number._missingData']={'condition':True}
        mismatch_checks[f'cp_{i}_Part / Process Number._missingData']={'reason':'missingData'}
    else:
        checks[f'cp_{i}_Part / Process Number._missingData']={'condition':False}

for i in control_data['dataframe']:
    if control_data['dataframe'][i]['Machine Device Jig Tool For Mfg.'].duplicated().any():
        checks[f'cp_{i}_Machine Device Jig Tool For Mfg._duplicate_data']={'condition':True}
        mismatch_checks[f'cp_{i}_Machine Device Jig Tool For Mfg._duplicate_data']={'reason':'duplicateData'}
    else:
        checks[f'cp_{i}_Machine Device Jig Tool For Mfg._duplicate_data']={'condition':False}

for i in control_data['dataframe']:
    if control_data['dataframe'][i]['Product/Process Specification.'].isna().any():
        checks[f'cp_{i}_Product/Process Specification._missingData']={'condition':True}
        mismatch_checks[f'cp_{i}_Product/Process Specification._missingData']={'reason':'missingData'}
    else:
        checks[f'cp_{i}_Product/Process Specification._missingData']={'condition':False}

for i in control_data['dataframe']:
    if control_data['dataframe'][i]['Evaluation Measurement Technique.'].isna().any():
        checks[f'cp_{i}_Evaluation Measurement Technique._missingData']={'condition':True}
        mismatch_checks[f'cp_{i}_Evaluation Measurement Technique._missingData']={'reason':'missingData'}
    else:
        checks[f'cp_{i}_Evaluation Measurement Technique._missingData']={'condition':False}

for i in control_data['dataframe']:
    if control_data['dataframe'][i]['Reaction Plan & Corrective action'].isna().any():
        checks[f'cp_{i}_Reaction Plan & Corrective action_missingData']={'condition':True}
        mismatch_checks[f'cp_{i}_Reaction Plan & Corrective action_missingData']={'reason':'missingData'}
    else:
        checks[f'cp_{i}_Reaction Plan & Corrective action_missingData']={'condition':False}

for i in control_data['dataframe']:
    if control_data['dataframe'][i]['Control Method.'].isna().any():
        checks[f'cp_{i}_Control Method._missingData']={'condition':True}
        mismatch_checks[f'cp_{i}_Control Method._missingData']={'reason':'missingData'}
    else:
        checks[f'cp_{i}_Control Method._missingData']={'condition':False}

for i in control_data['dataframe']:
    if control_data['dataframe'][i]['Frequency.'].isna().any():
        checks[f'cp_{i}_Frequency._missingData']={'condition':True}
        mismatch_checks[f'cp_{i}_Frequency._missingData']={'reason':'missingData'}
    else:
        checks[f'cp_{i}_Frequency._missingData']={'condition':False}

if control_data['dataframe'][i].shape[1]!=13:
    checks[f'cp_column_count_mismatch']={'condition':True}
else:
    checks[f'cp_column_count_mismatch']={'condition':False}
    mismatch_checks[f'cp_column_count_mismatch']={'reason':'columnCountMismatch'}



checks

rows = []
for key, value in mismatch_checks.items():
    row = {"key": key}
    row.update(value)
    rows.append(row)

# Convert to DataFrame
df = pd.DataFrame(rows)

# Save to Excel
df.to_excel("output.xlsx", index=False)



with open("data.json", "w") as json_file:
    json.dump(mismatch_checks, json_file, indent=4)

# Step 1: Generate content using Gemini
from openai import OpenAI 

def generate_pdf_content_with_gpt(context, api_key):
    client = OpenAI(api_key=api_key)

    prompt = (
        f"""
        Based on the following part manufacturing data, generate a detailed report
        Show all the values where condition is True and false both 
        we have 5 files as pfd: Process flow Diagram,
        cp or control_data : Control Plan,
        PSW: Part Submission Warrant,
        pfmea: Process Failure Mode and Effects Analysis,
        images or BOM: Bill of Materials
        on behalf of there files give a detailed report of each and every point.
        PDF-style report with clear section headers and bullet points:
        
        {context}
        
        The output should be in natural text or Markdown format."""
    )

    response = client.chat.completions.create(
        model="gpt-4o",
        messages=[
            {"role": "system", "content": "You are a technical assistant specialized in creating structured documents."},
            {"role": "user", "content": prompt}
        ],
        temperature=0.1,
        max_tokens=5048
    )

    return response.choices[0].message.content.strip()

# Step 2: Save to PDF using ReportLab
import re
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch

def write_pdf(text, output_file):
    doc = SimpleDocTemplate(output_file, pagesize=A4,
                            rightMargin=0.75 * inch, leftMargin=0.75 * inch,
                            topMargin=0.75 * inch, bottomMargin=0.75 * inch)

    # Define styles
    styles = getSampleStyleSheet()
    styles.add(ParagraphStyle(name='CustomHeading1', fontSize=16, leading=20, spaceAfter=10, spaceBefore=10))
    styles.add(ParagraphStyle(name='CustomHeading2', fontSize=14, leading=18, spaceAfter=8, spaceBefore=8))
    styles.add(ParagraphStyle(name='BulletBold', fontSize=11, leading=14, leftIndent=20, bulletIndent=10))
    styles.add(ParagraphStyle(name='CustomNormal', fontSize=11, leading=14))

    def convert_markdown_bold(line):
        # Replace **bold** with <b>bold</b>
        return re.sub(r"\*\*(.*?)\*\*", r"<b>\1</b>", line)

    content = []

    for line in text.split('\n'):
        line = line.strip()
        if not line:
            content.append(Spacer(1, 8))
            continue

        line = convert_markdown_bold(line)

        if line.startswith("# "):
            content.append(Paragraph(line[2:].strip(), styles["CustomHeading1"]))
        elif line.startswith("## "):
            content.append(Paragraph(line[3:].strip(), styles["CustomHeading2"]))
        elif re.match(r"\* <b>.*</b>:", line):
            # Special bullet with bold label
            match = re.findall(r"\* <b>(.*?)</b>:\s*(.*)", line)
            if match:
                label, value = match[0]
                content.append(Paragraph(f"<b>{label}:</b> {value}", styles["BulletBold"]))
            else:
                content.append(Paragraph(line[2:].strip(), styles["BulletBold"]))
        elif line.startswith("* "):
            content.append(Paragraph(line[2:].strip(), styles["BulletBold"]))
        else:
            content.append(Paragraph(line, styles["CustomNormal"]))

    doc.build(content)
    print(f"✅ PDF saved to {output_file}")



with open("output.txt", "w", encoding="utf-8") as file:
    file.write(str(checks))
print("text file printed")

# content = generate_pdf_content_with_gpt(checks, "********************************************************************************************************************************************************************")
# print("llm called")
# write_pdf(content, r"C:\Users\<USER>\Desktop\swaraj\output\output.pdf")





# %%
