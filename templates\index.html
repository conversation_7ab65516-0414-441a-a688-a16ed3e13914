<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SwaraJ AI - Document Processing</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 800px;
            width: 90%;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            color: #666;
            font-size: 1.1em;
        }

        .upload-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .upload-section:hover {
            border-color: #667eea;
            background-color: #f8f9ff;
        }

        .upload-section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .upload-section p {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 10px;
        }

        .file-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
        }

        .file-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .submit-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 15px 40px;
            border: none;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            width: 100%;
            margin-top: 20px;
            transition: all 0.3s ease;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .submit-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .loading {
            display: none;
            text-align: center;
            margin-top: 20px;
        }

        .loading.show {
            display: block;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .file-count {
            font-size: 0.8em;
            color: #667eea;
            margin-top: 5px;
        }

        .required {
            color: #e74c3c;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Swaraj AI</h1>
            <p>Advanced Document Processing System</p>
        </div>

        <form id="uploadForm" action="/process" method="post" enctype="multipart/form-data">
            <div class="upload-section">
                <h3>1. Images <span class="required">*</span></h3>
                <p>Upload multiple images (JPG, PNG, GIF)</p>
                <input type="file" name="images" class="file-input" accept="image/*" multiple required>
                <div class="file-count" id="images-count"></div>
            </div>

            <div class="upload-section">
                <h3>2. PSW Files <span class="required">*</span></h3>
                <p>Upload PSW documents (Excel, PDF, or Images)</p>
                <input type="file" name="psw_files" class="file-input" accept=".xlsx,.xls,.pdf,.jpg,.jpeg,.png" multiple required>
                <div class="file-count" id="psw-count"></div>
            </div>

            <div class="upload-section">
                <h3>3. CP Files <span class="required">*</span></h3>
                <p>Upload CP documents (Excel, PDF, or Images)</p>
                <input type="file" name="cp_files" class="file-input" accept=".xlsx,.xls,.pdf,.jpg,.jpeg,.png" multiple required>
                <div class="file-count" id="cp-count"></div>
            </div>

            <div class="upload-section">
                <h3>4. PFD Files <span class="required">*</span></h3>
                <p>Upload PFD documents (Excel, PDF, or Images)</p>
                <input type="file" name="pfd_files" class="file-input" accept=".xlsx,.xls,.pdf,.jpg,.jpeg,.png" multiple required>
                <div class="file-count" id="pfd-count"></div>
            </div>

            <div class="upload-section">
                <h3>5. PFMEA Files <span class="required">*</span></h3>
                <p>Upload PFMEA documents (Excel, PDF, or Images)</p>
                <input type="file" name="pfmea_files" class="file-input" accept=".xlsx,.xls,.pdf,.jpg,.jpeg,.png" multiple required>
                <div class="file-count" id="pfmea-count"></div>
            </div>

            <button type="submit" class="submit-btn" id="submitBtn">
                Process Documents
            </button>

            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>Processing your documents... Please wait.</p>
            </div>
        </form>
    </div>

    <script>
        // File count display
        document.querySelectorAll('.file-input').forEach(input => {
            input.addEventListener('change', function() {
                const countElement = document.getElementById(this.name.replace('_files', '') + '-count');
                if (countElement) {
                    const fileCount = this.files.length;
                    countElement.textContent = fileCount > 0 ? `${fileCount} file(s) selected` : '';
                }
            });
        });

        // Form submission
        document.getElementById('uploadForm').addEventListener('submit', function(e) {
            const submitBtn = document.getElementById('submitBtn');
            const loading = document.getElementById('loading');
            
            submitBtn.disabled = true;
            submitBtn.textContent = 'Processing...';
            loading.classList.add('show');
        });

        // Drag and drop enhancement
        document.querySelectorAll('.upload-section').forEach(section => {
            section.addEventListener('dragover', function(e) {
                e.preventDefault();
                this.style.borderColor = '#667eea';
                this.style.backgroundColor = '#f8f9ff';
            });

            section.addEventListener('dragleave', function(e) {
                e.preventDefault();
                this.style.borderColor = '#ddd';
                this.style.backgroundColor = 'transparent';
            });

            section.addEventListener('drop', function(e) {
                e.preventDefault();
                this.style.borderColor = '#ddd';
                this.style.backgroundColor = 'transparent';
                
                const fileInput = this.querySelector('.file-input');
                if (fileInput && e.dataTransfer.files.length > 0) {
                    fileInput.files = e.dataTransfer.files;
                    fileInput.dispatchEvent(new Event('change'));
                }
            });
        });
    </script>
</body>
</html>