<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Swarag AI - Processing Results</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .success-message {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
            font-size: 1.2em;
        }

        .file-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .file-category {
            background: #f8f9ff;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .file-category h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .file-list {
            list-style: none;
        }

        .file-list li {
            padding: 5px 0;
            color: #666;
            border-bottom: 1px solid #eee;
        }

        .file-list li:last-child {
            border-bottom: none;
        }

        .pdf-section {
            background: #f9f9f9;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
        }

        .pdf-section h2 {
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .pdf-viewer {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: white;
        }

        .download-section {
            text-align: center;
            margin-top: 20px;
        }

        .download-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 0 10px;
            transition: all 0.3s ease;
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .back-btn {
            background: linear-gradient(45deg, #95a5a6, #7f8c8d);
        }

        .stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin: 10px;
            min-width: 150px;
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            color: #666;
            font-size: 0.9em;
        }

        .no-pdf {
            text-align: center;
            padding: 40px;
            color: #666;
            font-style: italic;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            
            .pdf-viewer {
                height: 400px;
            }
            
            .stats {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Swaraj AI</h1>
            <p>Processing Complete</p>
        </div>

        <div class="success-message">
            ✅ All documents have been successfully processed!
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">{{ saved_files.images|length }}</div>
                <div class="stat-label">Images</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ saved_files.psw|length }}</div>
                <div class="stat-label">PSW Files</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ saved_files.cp|length }}</div>
                <div class="stat-label">CP Files</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ saved_files.pfd|length }}</div>
                <div class="stat-label">PFD Files</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ saved_files.pfmea|length }}</div>
                <div class="stat-label">PFMEA Files</div>
            </div>
        </div>

        <div class="file-summary">
            <div class="file-category">
                <h3>📷 Images</h3>
                <ul class="file-list">
                    {% for file in saved_files.images %}
                    <li>{{ file }}</li>
                    {% endfor %}
                </ul>
            </div>

            <div class="file-category">
                <h3>📋 PSW Files</h3>
                <ul class="file-list">
                    {% for file in saved_files.psw %}
                    <li>{{ file }}</li>
                    {% endfor %}
                </ul>
            </div>

            <div class="file-category">
                <h3>📊 CP Files</h3>
                <ul class="file-list">
                    {% for file in saved_files.cp %}
                    <li>{{ file }}</li>
                    {% endfor %}
                </ul>
            </div>

            <div class="file-category">
                <h3>📈 PFD Files</h3>
                <ul class="file-list">
                    {% for file in saved_files.pfd %}
                    <li>{{ file }}</li>
                    {% endfor %}
                </ul>
            </div>

            <div class="file-category">
                <h3>🔍 PFMEA Files</h3>
                <ul class="file-list">
                    {% for file in saved_files.pfmea %}
                    <li>{{ file }}</li>
                    {% endfor %}
                </ul>
            </div>
        </div>


        <div class="download-section">
            {% if output_pdf %}
            <a href="/download/{{ output_pdf }}" class="download-btn" download>
                📥 Download PDF Report
            </a>
            {% endif %}
            <a href="/" class="download-btn back-btn">
                🔄 Process New Documents
            </a>
        </div>
    </div>

    <script>
        // Auto-refresh PDF viewer if needed
        window.addEventListener('load', function() {
            const iframe = document.querySelector('.pdf-viewer');
            if (iframe) {
                // Fallback for browsers that don't support PDF viewing
                iframe.addEventListener('error', function() {
                    this.style.display = 'none';
                    const fallback = document.createElement('div');
                    fallback.innerHTML = '<p>PDF preview not available. Please download the file to view it.</p>';
                    fallback.className = 'no-pdf';
                    this.parentNode.insertBefore(fallback, this);
                });
            }
        });
    </script>
</body>
</html>